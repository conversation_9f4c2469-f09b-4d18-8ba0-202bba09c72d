"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Truck,
    Package,
    MapPin,
    Clock,
    DollarSign,
    Settings,
    Plus,
    Edit,
    Trash2,
    MoreHorizontal,
    Calendar,
    TrendingUp,
    AlertTriangle
} from "lucide-react";

// Mock data for shipping zones
const shippingZones = [
    {
        id: 1,
        name: "Continental US",
        regions: ["All US States except AK, HI"],
        method: "Standard Ground",
        rate: "$12.99",
        estimatedDays: "5-7",
        isActive: true,
    },
    {
        id: 2,
        name: "Alaska & Hawaii",
        regions: ["AK", "HI"],
        method: "Express Air",
        rate: "$24.99",
        estimatedDays: "3-5",
        isActive: true,
    },
    {
        id: 3,
        name: "Express Shipping",
        regions: ["All US States"],
        method: "Next Day Air",
        rate: "$39.99",
        estimatedDays: "1-2",
        isActive: true,
    },
    {
        id: 4,
        name: "Free Shipping",
        regions: ["All US States"],
        method: "Standard Ground",
        rate: "Free",
        estimatedDays: "5-7",
        isActive: true,
        minOrder: "$150.00",
    },
];

// Mock data for carriers
const carriers = [
    {
        id: 1,
        name: "FedEx",
        isActive: true,
        services: ["Ground", "Express", "Overnight"],
        apiStatus: "Connected",
    },
    {
        id: 2,
        name: "UPS",
        isActive: true,
        services: ["Ground", "Next Day Air", "2nd Day Air"],
        apiStatus: "Connected",
    },
    {
        id: 3,
        name: "USPS",
        isActive: false,
        services: ["Priority Mail", "Express Mail"],
        apiStatus: "Not Connected",
    },
];

// Mock data for recent shipments
const recentShipments = [
    {
        id: "SHIP-001",
        orderId: "ORD-1234",
        customer: "Dr. Sarah Wilson",
        carrier: "FedEx",
        trackingNumber: "1234567890123456",
        status: "In Transit",
        destination: "Boston, MA",
        estimatedDelivery: "2024-06-18",
        createdAt: "2024-06-15",
    },
    {
        id: "SHIP-002",
        orderId: "ORD-1235",
        customer: "Prof. Michael Chen",
        carrier: "UPS",
        trackingNumber: "1Z9876543210987654",
        status: "Delivered",
        destination: "Stanford, CA",
        estimatedDelivery: "2024-06-16",
        createdAt: "2024-06-14",
    },
    {
        id: "SHIP-003",
        orderId: "ORD-1236",
        customer: "Dr. Emily Rodriguez",
        carrier: "FedEx",
        trackingNumber: "9876543210987654321",
        status: "Pending",
        destination: "New York, NY",
        estimatedDelivery: "2024-06-19",
        createdAt: "2024-06-16",
    },
];

const StatusBadge = ({ status }: { status: string }) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
        "In Transit": "default",
        "Delivered": "secondary",
        "Pending": "outline",
        "Exception": "destructive",
    };

    return <Badge variant={variants[status]}>{status}</Badge>;
};

export function ShippingContent() {
    const [selectedZone, setSelectedZone] = useState<number | null>(null);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Shipping</h1>
                    <p className="text-muted-foreground">
                        Manage shipping zones, carriers, and track deliveries.
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline">
                        <Calendar className="h-4 w-4 mr-2" />
                        Shipping Reports
                    </Button>
                    <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Shipping Zone
                    </Button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Active Shipments</CardTitle>
                        <Truck className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">145</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +12% from last week
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">96.8%</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +0.3% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Avg. Shipping Cost</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">$16.45</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-red-500" />
                            +$1.20 from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Delivery Issues</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">3</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-red-500" />
                            2 more than last week
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Tabs */}
            <Tabs defaultValue="zones" className="space-y-6">
                <TabsList>
                    <TabsTrigger value="zones">Shipping Zones</TabsTrigger>
                    <TabsTrigger value="carriers">Carriers</TabsTrigger>
                    <TabsTrigger value="shipments">Recent Shipments</TabsTrigger>
                    <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>

                {/* Shipping Zones Tab */}
                <TabsContent value="zones" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Shipping Zones</CardTitle>
                            <CardDescription>
                                Configure shipping rates and delivery times for different regions
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Zone Name</TableHead>
                                        <TableHead>Regions</TableHead>
                                        <TableHead>Method</TableHead>
                                        <TableHead>Rate</TableHead>
                                        <TableHead>Delivery Time</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead></TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {shippingZones.map((zone) => (
                                        <TableRow key={zone.id}>
                                            <TableCell className="font-medium">{zone.name}</TableCell>
                                            <TableCell>
                                                <div className="text-sm text-muted-foreground">
                                                    {zone.regions.join(", ")}
                                                </div>
                                            </TableCell>
                                            <TableCell>{zone.method}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    {zone.rate}
                                                    {zone.minOrder && (
                                                        <Badge variant="outline" className="text-xs">
                                                            Min: {zone.minOrder}
                                                        </Badge>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>{zone.estimatedDays} business days</TableCell>
                                            <TableCell>
                                                <Badge variant={zone.isActive ? "default" : "secondary"}>
                                                    {zone.isActive ? "Active" : "Inactive"}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem>
                                                            <Edit className="h-4 w-4 mr-2" />
                                                            Edit Zone
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <Settings className="h-4 w-4 mr-2" />
                                                            Configure Rates
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem className="text-red-600">
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Delete Zone
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Carriers Tab */}
                <TabsContent value="carriers" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Carriers List */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Shipping Carriers</CardTitle>
                                <CardDescription>
                                    Manage your shipping carrier integrations
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {carriers.map((carrier) => (
                                    <div key={carrier.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center gap-4">
                                            <div className="h-12 w-12 bg-muted rounded-lg flex items-center justify-center">
                                                <Truck className="h-6 w-6" />
                                            </div>
                                            <div>
                                                <div className="flex items-center gap-2">
                                                    <span className="font-medium">{carrier.name}</span>
                                                    <Badge
                                                        variant={carrier.apiStatus === "Connected" ? "default" : "destructive"}
                                                    >
                                                        {carrier.apiStatus}
                                                    </Badge>
                                                </div>
                                                <div className="text-sm text-muted-foreground">
                                                    Services: {carrier.services.join(", ")}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Switch checked={carrier.isActive} />
                                            <Button variant="outline" size="sm">
                                                Configure
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        {/* Add New Carrier */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Add New Carrier</CardTitle>
                                <CardDescription>
                                    Connect a new shipping carrier
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="carrier-name">Carrier Name</Label>
                                    <Select>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a carrier" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="dhl">DHL</SelectItem>
                                            <SelectItem value="amazon">Amazon Logistics</SelectItem>
                                            <SelectItem value="ontrac">OnTrac</SelectItem>
                                            <SelectItem value="custom">Custom Carrier</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="api-key">API Key</Label>
                                    <Input id="api-key" type="password" placeholder="Enter API key" />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="account-number">Account Number</Label>
                                    <Input id="account-number" placeholder="Enter account number" />
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch id="test-mode" />
                                    <Label htmlFor="test-mode">Test mode</Label>
                                </div>

                                <Button className="w-full">
                                    Connect Carrier
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Recent Shipments Tab */}
                <TabsContent value="shipments" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Shipments</CardTitle>
                            <CardDescription>
                                Track and manage your recent shipments
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Shipment ID</TableHead>
                                        <TableHead>Order</TableHead>
                                        <TableHead>Customer</TableHead>
                                        <TableHead>Carrier</TableHead>
                                        <TableHead>Tracking Number</TableHead>
                                        <TableHead>Destination</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Est. Delivery</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {recentShipments.map((shipment) => (
                                        <TableRow key={shipment.id}>
                                            <TableCell className="font-medium">{shipment.id}</TableCell>
                                            <TableCell>{shipment.orderId}</TableCell>
                                            <TableCell>{shipment.customer}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Truck className="h-4 w-4" />
                                                    {shipment.carrier}
                                                </div>
                                            </TableCell>
                                            <TableCell className="font-mono text-sm">
                                                {shipment.trackingNumber}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-1">
                                                    <MapPin className="h-4 w-4" />
                                                    {shipment.destination}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <StatusBadge status={shipment.status} />
                                            </TableCell>
                                            <TableCell>{shipment.estimatedDelivery}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Settings Tab */}
                <TabsContent value="settings" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Default Settings</CardTitle>
                                <CardDescription>
                                    Configure default shipping preferences
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="default-carrier">Default Carrier</Label>
                                    <Select>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select default carrier" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="fedex">FedEx</SelectItem>
                                            <SelectItem value="ups">UPS</SelectItem>
                                            <SelectItem value="usps">USPS</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="package-weight">Default Package Weight (lbs)</Label>
                                    <Input id="package-weight" type="number" placeholder="1.0" />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="dimensions">Default Dimensions (L x W x H)</Label>
                                    <div className="grid grid-cols-3 gap-2">
                                        <Input placeholder="Length" />
                                        <Input placeholder="Width" />
                                        <Input placeholder="Height" />
                                    </div>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch id="insurance" />
                                    <Label htmlFor="insurance">Add insurance by default</Label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch id="signature" />
                                    <Label htmlFor="signature">Require signature</Label>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Notifications</CardTitle>
                                <CardDescription>
                                    Configure shipping notifications
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="ship-notification">Shipment Created</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Notify customers when shipment is created
                                        </p>
                                    </div>
                                    <Switch id="ship-notification" />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="transit-notification">In Transit</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Send updates when package is in transit
                                        </p>
                                    </div>
                                    <Switch id="transit-notification" />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="delivery-notification">Delivered</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Confirm delivery to customers
                                        </p>
                                    </div>
                                    <Switch id="delivery-notification" />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="exception-notification">Exceptions</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Alert for delivery exceptions
                                        </p>
                                    </div>
                                    <Switch id="exception-notification" />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}
