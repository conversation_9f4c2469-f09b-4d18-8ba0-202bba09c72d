'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { MoreHorizontal, Edit, Trash2, Package, Eye, Settings } from 'lucide-react';
import { Product, formatDate, formatCurrency } from '@/lib/api';
import { usePermissions } from '@/contexts/auth-context';
import { saveAs } from 'file-saver';

interface ProductsTableProps {
  products: Product[];
  loading: boolean;
  onEdit: (product: Product) => void;
  onDelete: (productId: string) => void;
  onManageVariants: (product: Product) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

function productsToCSV(products: Product[]): string {
  const headers = ['Name', 'SKU', 'Status', 'Price'];
  const rows = products.map(p => [
    p.name,
    p.variants?.[0]?.sku || '',
    p.status,
    p.variants?.[0]?.regularPrice?.toString() || ''
  ]);
  return [headers, ...rows].map(r => r.map(x => `"${x}"`).join(',')).join('\n');
}

export function ProductsTable({
  products,
  loading,
  onEdit,
  onDelete,
  onManageVariants,
  currentPage,
  totalPages,
  onPageChange,
}: ProductsTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [selected, setSelected] = useState<string[]>([]);
  const [bulkAction, setBulkAction] = useState<null | 'delete' | 'export'>(null);
  const { canDelete, canUpdate } = usePermissions();

  const allSelected = products.length > 0 && selected.length === products.length;
  const toggleSelectAll = () => {
    if (allSelected) setSelected([]);
    else setSelected(products.map(p => p.id));
  };
  const toggleSelect = (id: string) => {
    setSelected(sel => sel.includes(id) ? sel.filter(x => x !== id) : [...sel, id]);
  };

  const handleBulkDelete = async () => {
    if (selected.length === 0) return;
    if (!window.confirm(`Delete ${selected.length} products?`)) return;
    // Call backend bulk delete endpoint
    await fetch('/api/products/bulk-delete', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ids: selected }),
    });
    setSelected([]);
    setBulkAction(null);
    // Optionally, refresh products list here
  };

  const handleBulkExport = () => {
    const selectedProducts = products.filter(p => selected.includes(p.id));
    const csv = productsToCSV(selectedProducts);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, 'products-export.csv');
    setBulkAction(null);
  };

  const handleDeleteClick = (product: Product) => {
    setProductToDelete(product);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (productToDelete) {
      onDelete(productToDelete.id);
      setDeleteDialogOpen(false);
      setProductToDelete(null);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'default';
      case 'DRAFT':
        return 'secondary';
      case 'INACTIVE':
        return 'outline';
      case 'ARCHIVED':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      case 'ARCHIVED':
        return 'bg-red-100 text-red-800 hover:bg-red-100';
      default:
        return '';
    }
  };

  const getPriceRange = (product: Product) => {
    if (!product.variants || product.variants.length === 0) {
      return 'No variants';
    }

    // Get all prices including segment prices
    const prices = product.variants.flatMap(v => {
      const prices = [v.regularPrice];
      if (v.salePrice) prices.push(v.salePrice);
      if (v.segmentPrices) {
        prices.push(...v.segmentPrices.map(sp => sp.regularPrice));
        prices.push(...v.segmentPrices.map(sp => sp.salePrice || 0).filter(p => p > 0));
      }
      return prices;
    });

    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);

    if (minPrice === maxPrice) {
      return formatCurrency(minPrice);
    }

    return `${formatCurrency(minPrice)} - ${formatCurrency(maxPrice)}`;
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4">
            <div className="w-16 h-16 bg-gray-200 rounded-md animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
              <div className="h-3 bg-gray-200 rounded animate-pulse w-1/6" />
            </div>
            <div className="h-8 bg-gray-200 rounded animate-pulse w-20" />
            <div className="h-8 bg-gray-200 rounded animate-pulse w-8" />
          </div>
        ))}
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-8">
        <Package className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No products</h3>
        <p className="mt-1 text-sm text-gray-500">Get started by creating a new product.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead><input type="checkbox" checked={allSelected} onChange={toggleSelectAll} /></TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Categories</TableHead>
              <TableHead>Variants</TableHead>
              <TableHead>Total Inventory</TableHead>
              <TableHead>Price Range</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.map((product) => (
              <TableRow key={product.id}>
                <TableCell><input type="checkbox" checked={selected.includes(product.id)} onChange={() => toggleSelect(product.id)} /></TableCell>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                      {product.images && product.images.length > 0 ? (
                        <img
                          src={product.images[0].url}
                          alt={product.images[0].altText || product.name}
                          className="w-full h-full object-cover rounded-md"
                        />
                      ) : (
                        <Package className="h-6 w-6 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium">{product.name}</div>
                      <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                        {product.description || 'No description'}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge 
                    variant={getStatusBadgeVariant(product.status)}
                    className={getStatusColor(product.status)}
                  >
                    {product.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1 max-w-[150px]">
                    {product.categories && product.categories.length > 0 ? (
                      product.categories.slice(0, 2).map((category, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {category.name}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-muted-foreground">No categories</span>
                    )}
                    {product.categories && product.categories.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{product.categories.length - 2}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">
                      {product._count?.variants || 0} variants
                    </Badge>
                    {product.variants && product.variants.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onManageVariants(product)}
                        className="h-7 px-2"
                      >
                        <Settings className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {product.variants && product.variants.length > 0
                    ? product.variants.reduce((sum, variant) =>
                        sum + (variant.inventory ? variant.inventory.reduce((invSum, inv) => invSum + (inv.quantity || 0), 0) : 0)
                      , 0)
                    : 0}
                </TableCell>
                <TableCell>
                  <span className="font-medium">{getPriceRange(product)}</span>
                </TableCell>
                <TableCell>
                  <span className="text-sm text-muted-foreground">
                    {formatDate(product.createdAt)}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Product
                      </DropdownMenuItem>
                      {canUpdate('PRODUCTS') && (
                        <DropdownMenuItem onClick={() => onEdit(product)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Product
                        </DropdownMenuItem>
                      )}
                      {canUpdate('PRODUCTS') && (
                        <DropdownMenuItem onClick={() => onManageVariants(product)}>
                          <Settings className="mr-2 h-4 w-4" />
                          Manage Variants
                        </DropdownMenuItem>
                      )}
                      {canDelete('PRODUCTS') && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteClick(product)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Product
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </div>
          <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={onPageChange} />
        </div>
      )}

      {/* Bulk Actions Toolbar */}
      {selected.length > 0 && (
        <div className="bulk-actions-toolbar flex items-center justify-end space-x-2 p-2 bg-gray-50 rounded-md">
          <span className="text-sm text-muted-foreground">
            {selected.length} selected
          </span>
          <Button variant="outline" onClick={() => setBulkAction('delete')}>
            Delete Selected ({selected.length})
          </Button>
          <Button variant="outline" onClick={handleBulkExport}>
            Export Selected ({selected.length})
          </Button>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Product</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete{' '}
              <strong>{productToDelete?.name}</strong>?{' '}
              This will also delete all associated variants and cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Product
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}