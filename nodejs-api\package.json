{"name": "nodejs-api", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www", "dev": "nodemon ./bin/www", "migrate": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "migrate:reset": "prisma migrate reset", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "seed": "node prisma/seed.js"}, "dependencies": {"@prisma/client": "^6.11.1", "bcryptjs": "^2.4.3", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "dotenv": "^16.4.5", "express": "~4.16.1", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.0", "helmet": "^8.0.0", "http-errors": "~1.6.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "~1.9.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.15", "slugify": "^1.6.6", "stripe": "^17.5.0", "uuid": "^11.0.3"}, "devDependencies": {"nodemon": "^3.1.9", "prisma": "^5.22.0"}}