require("dotenv").config();
const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const rateLimit = require("express-rate-limit");
const { body, validationResult } = require("express-validator");
const createError = require("http-errors");
const path = require("path");
const cookieParser = require("cookie-parser");
const logger = require("morgan");
const multer = require("multer");

// Import Prisma client
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

// Import middleware
const { authMiddleware } = require("./middleware/auth");
const { errorHandler } = require("./middleware/errorHandler");
const validateRequest = require("./middleware/validateRequest");

// Import routes
const authRoutes = require("./routes/auth");
const userRoutes = require("./routes/users");
const customerRoutes = require("./routes/customers");
const productRoutes = require("./routes/products");
const orderRoutes = require("./routes/orders");
const inventoryRoutes = require("./routes/inventory");
const paymentRoutes = require("./routes/payments");
const shippingRoutes = require("./routes/shipping");
const promotionRoutes = require("./routes/promotions");
const analyticsRoutes = require("./routes/analytics");
const settingRoutes = require("./routes/settings");
const reviewRoutes = require("./routes/reviews");
const inventoryBatchesRoutes = require("./routes/inventory-batches");
const locationsRoutes = require("./routes/locations");
const categoriesRouter = require("./routes/categories");
const productsRouter = require("./routes/products");
const shippingRouter = require("./routes/shipping");
const usersRouter = require("./routes/users");
const collectionsRouter = require("./routes/collections");
const taxRatesRoutes = require("./routes/taxRates");
const transactionsRoutes = require("./routes/transactions");

const app = express();

// Security middleware
app.use(helmet());

// CORS configuration
app.use(
  cors({
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  })
);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: "Too many requests from this IP, please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use("/api/", limiter);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));
app.use(cookieParser());

// Logging middleware
app.use(logger("combined"));

// File upload configuration
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, process.env.UPLOAD_PATH || "./uploads");
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(
      null,
      file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname)
    );
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
  },
  fileFilter: function (req, file, cb) {
    // Allow only images and documents
    if (
      file.mimetype.startsWith("image/") ||
      file.mimetype.startsWith("application/")
    ) {
      cb(null, true);
    } else {
      cb(new Error("Only image and document files are allowed"), false);
    }
  },
});

// Static files
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || "development",
  });
});

// API documentation endpoint
app.get("/api", (req, res) => {
  res.json({
    message: "Centre Research Peptide Store API",
    version: "1.0.0",
    endpoints: {
      auth: "/api/auth",
      users: "/api/users",
      customers: "/api/customers",
      products: "/api/products",
      orders: "/api/orders",
      inventory: "/api/inventory",
      payments: "/api/payments",
      shipping: "/api/shipping",
      promotions: "/api/promotions",
      analytics: "/api/analytics",
      settings: "/api/settings",
      reviews: "/api/reviews",
      "inventory-batches": "/api/inventory-batches",
      locations: "/api/locations",
    },
    documentation: "https://docs.centreresearch.com/api",
  });
});

// API Routes
app.use("/api/auth", authRoutes);
app.use("/api/users", authMiddleware, userRoutes);
app.use("/api/customers", authMiddleware, customerRoutes);
app.use("/api/products", authMiddleware, productRoutes);
app.use("/api/orders", authMiddleware, orderRoutes);
app.use("/api/inventory", authMiddleware, inventoryRoutes);
app.use("/api/payments", authMiddleware, paymentRoutes);
app.use("/api/shipping", authMiddleware, shippingRoutes);
app.use("/api/promotions", authMiddleware, promotionRoutes);
app.use("/api/analytics", authMiddleware, analyticsRoutes);
app.use("/api/settings", authMiddleware, settingRoutes);
app.use("/api/reviews", authMiddleware, reviewRoutes);
app.use("/api/inventory-batches", authMiddleware, inventoryBatchesRoutes);
app.use("/api/locations", authMiddleware, locationsRoutes);
app.use("/api/categories", categoriesRouter);
app.use("/api/products", productsRouter);
app.use("/api/shipping", shippingRouter);
app.use("/api/users", usersRouter);
app.use("/api/collections", collectionsRouter);
app.use("/api/tax-rates", authMiddleware, taxRatesRoutes);
app.use("/api/transactions", authMiddleware, transactionsRoutes);

// File upload endpoint
app.post("/api/upload", authMiddleware, upload.single("file"), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: "No file uploaded" });
  }

  res.json({
    message: "File uploaded successfully",
    file: {
      filename: req.file.filename,
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      url: `/uploads/${req.file.filename}`,
    },
  });
});

// Stripe webhook endpoint (before body parser)
app.post("/api/webhooks/stripe", express.json(), (req, res) => {
  const sig = req.headers["stripe-signature"];
  // Handle Stripe webhook
  res.json({ received: true });
});

// Global error handler
app.use(errorHandler);

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    error: "Not Found",
    message: "The requested resource was not found",
    path: req.originalUrl,
  });
});

// Graceful shutdown
process.on("SIGTERM", async () => {
  console.log("SIGTERM received, shutting down gracefully");
  await prisma.$disconnect();
  process.exit(0);
});

process.on("SIGINT", async () => {
  console.log("SIGINT received, shutting down gracefully");
  await prisma.$disconnect();
  process.exit(0);
});

module.exports = app;
