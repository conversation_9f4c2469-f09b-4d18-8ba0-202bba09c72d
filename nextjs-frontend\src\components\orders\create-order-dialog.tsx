'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Plus, Trash2, Search, Package, User, CreditCard, ChevronDown, Check } from 'lucide-react';
import { api, Customer, Product, ProductVariant, Address, Promotion, TaxRate } from '@/lib/api';
import { toast } from 'sonner';
import { Country, State } from 'country-state-city';

interface CreateOrderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface OrderItem {
  variantId: string;
  variant?: ProductVariant & { 
    product?: Product;
    inventory?: {
      id: string;
      locationId: string;
      quantity: number;
      reservedQty: number;
    }[];
    segmentPrices?: {
      id: string;
      customerType: 'B2C' | 'B2B' | 'WHOLESALE';
      regularPrice: number;
      salePrice?: number;
    }[];
  };
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export function CreateOrderDialog({ open, onOpenChange, onSuccess }: CreateOrderDialogProps) {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [availableCoupons, setAvailableCoupons] = useState<Promotion[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [couponSearchOpen, setCouponSearchOpen] = useState(false);
  
  // Form state
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedBillingAddress, setSelectedBillingAddress] = useState<Address | null>(null);
  const [selectedShippingAddress, setSelectedShippingAddress] = useState<Address | null>(null);
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [shippingAmount, setShippingAmount] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [notes, setNotes] = useState('');
  const [couponCode, setCouponCode] = useState('');
  const [couponStatus, setCouponStatus] = useState<'idle' | 'valid' | 'invalid' | 'checking'>('idle');
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
  const [applicableTaxRate, setApplicableTaxRate] = useState<TaxRate | null>(null);
  const [taxRateLoading, setTaxRateLoading] = useState(false);
  const couponInputRef = useRef<HTMLInputElement>(null);

  // Load customers and products
  useEffect(() => {
    if (open) {
      fetchCustomers();
      fetchProducts();
      fetchAvailableCoupons();
    }
  }, [open]);

  const fetchCustomers = async () => {
    try {
      const response = await api.getCustomers({ limit: 100 });
      if (response.success && response.data) {
        setCustomers(response.data.customers || []);
      }
    } catch (error) {
      console.error('Failed to fetch customers:', error);
    }
  };

  const fetchAvailableCoupons = async () => {
    try {
      const response = await api.getPromotions({ isActive: true, limit: 100 });
      if (response.success && response.data) {
        // Handle both possible response structures
        const coupons = Array.isArray(response.data) ? response.data : response.data.data || [];
        // Filter for currently valid coupons
        const now = new Date();
        const validCoupons = coupons.filter((coupon: Promotion) => {
          const isNotExpired = !coupon.expiresAt || new Date(coupon.expiresAt) >= now;
          const isStarted = !coupon.startsAt || new Date(coupon.startsAt) <= now;
          const hasUsageLeft = !coupon.usageLimit || coupon.usageCount < coupon.usageLimit;
          return coupon.isActive && isNotExpired && isStarted && hasUsageLeft;
        });
        setAvailableCoupons(validCoupons);
      }
    } catch (error) {
      console.error('Failed to fetch coupons:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await api.getProducts({ 
        limit: 100, 
        status: 'ACTIVE',
        include: {
          variants: {
            include: {
              inventory: true
            }
          }
        }
      });
      
      // Debug logging
      console.log('Products API response:', response);
      
      // Validate inventory data
      const productsWithInventory = (response.success && response.data?.products || []).map((product: Product) => ({
        ...product,
        variants: product.variants?.map((variant: ProductVariant) => ({
          ...variant,
          inventory: variant.inventory || []
        }))
      }));
      
      console.log('Products with validated inventory:', productsWithInventory);
      setProducts(productsWithInventory);
    } catch (error) {
      console.error('Failed to fetch products:', error);
      toast.error('Failed to load products');
    }
  };

  const handleCustomerSelect = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    setSelectedCustomer(customer || null);
    
    // When customer changes, update prices of existing items based on their segment
    if (customer?.customerType && orderItems.length > 0) {
      setOrderItems(orderItems.map(item => {
        const segmentPrice = item.variant?.segmentPrices?.find(
          sp => sp.customerType === customer.customerType
        );
        
        const newUnitPrice = segmentPrice 
          ? (segmentPrice.salePrice || segmentPrice.regularPrice)
          : (item.variant?.salePrice || item.variant?.regularPrice || 0);

        return {
          ...item,
          unitPrice: newUnitPrice,
          totalPrice: item.quantity * newUnitPrice
        };
      }));
    }
    
    if (customer?.addresses && customer.addresses.length > 0) {
      const billingAddress = customer.addresses.find(a => a.type === 'BILLING' && a.isDefault);
      const shippingAddress = customer.addresses.find(a => a.type === 'SHIPPING' && a.isDefault);

      setSelectedBillingAddress(billingAddress || customer.addresses[0]);
      const selectedShipping = shippingAddress || customer.addresses[0];
      setSelectedShippingAddress(selectedShipping);

      // Fetch applicable tax rate for the selected shipping address
      if (selectedShipping) {
        fetchApplicableTaxRate(selectedShipping);
      }
    }
  };

  const handleAddOrderItem = (variant: ProductVariant & { product?: Product }) => {
    // Debug logging
    console.log('Variant inventory:', variant.inventory);
    
    // Check if variant has available inventory
    const totalAvailable = variant.inventory?.reduce((sum, inv) => {
      const available = Math.max(0, (inv.quantity || 0) - (inv.reservedQty || 0));
      console.log(`Location ${inv.locationId}: quantity=${inv.quantity}, reservedQty=${inv.reservedQty}, available=${available}`);
      return sum + available;
    }, 0) || 0;

    console.log('Total available:', totalAvailable);

    if (totalAvailable <= 0) {
      toast.error('This product is out of stock');
      return;
    }

    const existingItem = orderItems.find(item => item.variantId === variant.id);
    
    if (existingItem) {
      // Check if increasing quantity is possible
      if (existingItem.quantity + 1 > totalAvailable) {
        toast.error('Not enough stock available');
        return;
      }

      setOrderItems(orderItems.map(item =>
        item.variantId === variant.id
          ? { ...item, quantity: item.quantity + 1, totalPrice: Math.max(0, (item.quantity + 1) * item.unitPrice) }
          : item
      ));
    } else {
      // Get price based on customer segment
      const segmentPrice = selectedCustomer?.customerType && variant.segmentPrices?.find(
        sp => sp.customerType === selectedCustomer.customerType
      );

      const unitPrice = segmentPrice 
        ? (segmentPrice.salePrice || segmentPrice.regularPrice)
        : (variant.salePrice || variant.regularPrice);

      const newItem: OrderItem = {
        variantId: variant.id,
        variant,
        quantity: 1,
        unitPrice,
        totalPrice: Math.max(0, unitPrice),
      };
      setOrderItems([...orderItems, newItem]);
    }
  };

  const handleRemoveOrderItem = (variantId: string) => {
    setOrderItems(orderItems.filter(item => item.variantId !== variantId));
  };

  const handleUpdateQuantity = (variantId: string, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveOrderItem(variantId);
      return;
    }

    const item = orderItems.find(item => item.variantId === variantId);
    if (!item?.variant?.inventory) return;

    // Check if requested quantity is available
    const totalAvailable = item.variant.inventory.reduce((sum, inv) => 
      sum + Math.max(0, inv.quantity - inv.reservedQty), 0);

    if (quantity > totalAvailable) {
      toast.error('Not enough stock available');
      return;
    }
    
    setOrderItems(orderItems.map(item =>
      item.variantId === variantId
        ? { ...item, quantity, totalPrice: Math.max(0, quantity * item.unitPrice) }
        : item
    ));
  };

  const handleUpdatePrice = (variantId: string, unitPrice: number) => {
    setOrderItems(orderItems.map(item =>
      item.variantId === variantId
        ? { ...item, unitPrice, totalPrice: Math.max(0, item.quantity * unitPrice) }
        : item
    ));
  };

  // Convert country and state names to ISO codes for tax rate lookup
  const getCountryStateIsoCodes = (countryName: string, stateName?: string) => {
    // Find country by name
    const country = Country.getAllCountries().find(c =>
      c.name.toLowerCase() === countryName.toLowerCase()
    );

    if (!country) {
      console.warn(`Country not found: ${countryName}`);
      return { countryCode: null, stateCode: null };
    }

    let stateCode = null;
    if (stateName) {
      // Find state by name within the country
      const state = State.getStatesOfCountry(country.isoCode).find(s =>
        s.name.toLowerCase() === stateName.toLowerCase()
      );
      stateCode = state?.isoCode || null;
      if (!state) {
        console.warn(`State not found: ${stateName} in ${countryName}`);
      }
    }

    return { countryCode: country.isoCode, stateCode };
  };

  // Fetch applicable tax rate based on shipping address
  const fetchApplicableTaxRate = async (address: Address) => {
    if (!address.country) return;

    setTaxRateLoading(true);
    try {
      // Convert country and state names to ISO codes
      const { countryCode, stateCode } = getCountryStateIsoCodes(address.country, address.state);

      if (!countryCode) {
        console.error('Could not find country code for:', address.country);
        setApplicableTaxRate(null);
        setTaxAmount(0);
        return;
      }

      console.log(`Looking up tax rate for: ${countryCode}, ${stateCode || 'no state'}`);

      const response = await api.getApplicableTaxRate(countryCode, stateCode || undefined);
      if (response.success && response.data) {
        console.log('Found tax rate:', response.data);
        setApplicableTaxRate(response.data);
        // Auto-calculate tax amount based on current subtotal
        const subtotal = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);
        const calculatedTax = subtotal * (response.data.rate / 100);
        setTaxAmount(calculatedTax);
      } else {
        console.log('No tax rate found for:', countryCode, stateCode);
        setApplicableTaxRate(null);
        setTaxAmount(0);
      }
    } catch (error) {
      console.error('Failed to fetch tax rate:', error);
      setApplicableTaxRate(null);
      setTaxAmount(0);
    } finally {
      setTaxRateLoading(false);
    }
  };

  const calculateCouponDiscount = async (coupon: Promotion, subtotal: number, shippingAmount: number): Promise<number> => {
    if (!coupon) return 0;

    // Check minimum order amount
    if (coupon.minOrderAmount && subtotal < parseFloat(coupon.minOrderAmount.toString())) {
      return 0;
    }

    // For BOGO and VOLUME_DISCOUNT, we need to call the backend for proper calculation
    if (coupon.type === 'BOGO' || coupon.type === 'VOLUME_DISCOUNT') {
      try {
        // Prepare order items data for backend calculation
        const orderItemsData = orderItems.map(item => ({
          variantId: item.variantId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          variant: {
            productId: item.variant?.productId
          }
        }));

        // Call backend to calculate advanced promotion discount
        const response = await api.calculatePromotionDiscount({
          promotionCode: coupon.code,
          orderItems: orderItemsData,
          customerId: selectedCustomer?.id,
          subtotal,
          shippingAmount
        });

        if (response.success && response.data) {
          return response.data.discount || 0;
        }
      } catch (error) {
        console.error('Failed to calculate advanced promotion discount:', error);
        // Fallback to simple calculation below
      }
    }

    // Simple calculation for basic promotion types or fallback
    let discount = 0;

    switch (coupon.type) {
      case 'PERCENTAGE':
        discount = subtotal * (parseFloat(coupon.value.toString()) / 100);
        break;
      case 'FIXED_AMOUNT':
        discount = parseFloat(coupon.value.toString());
        break;
      case 'FREE_SHIPPING':
        discount = shippingAmount;
        break;
      case 'BOGO':
        // Fallback simple BOGO logic if backend call fails
        const totalQuantity = orderItems.reduce((sum, item) => sum + item.quantity, 0);

        // Default to Buy 2 Get 1 Free if no specific configuration
        const buyQty = 2;
        const getQty = 1;
        const freeItems = Math.floor(totalQuantity / buyQty) * getQty;

        if (freeItems > 0) {
          // Calculate discount based on cheapest items getting free
          const sortedItems = [...orderItems].sort((a, b) => a.unitPrice - b.unitPrice);
          let remainingFreeQty = freeItems;

          for (const item of sortedItems) {
            if (remainingFreeQty <= 0) break;
            const freeQtyForItem = Math.min(remainingFreeQty, item.quantity);
            discount += freeQtyForItem * item.unitPrice;
            remainingFreeQty -= freeQtyForItem;
          }
        }
        break;
      case 'VOLUME_DISCOUNT':
        // Fallback volume discount logic
        const totalQty = orderItems.reduce((sum, item) => sum + item.quantity, 0);
        if (totalQty >= 10) {
          discount = subtotal * 0.1; // 10% for 10+ items
        } else if (totalQty >= 5) {
          discount = subtotal * 0.05; // 5% for 5+ items
        }
        break;
      default:
        discount = 0;
    }

    // Apply maximum discount limit if set
    if (coupon.maxDiscount) {
      discount = Math.min(discount, parseFloat(coupon.maxDiscount.toString()));
    }

    // Don't exceed subtotal
    return Math.min(discount, subtotal);
  };

  const handleCouponSelect = async (coupon: Promotion | null) => {
    if (coupon) {
      setCouponCode(coupon.code);
      setAppliedCoupon(coupon);
      setCouponStatus('valid');

      // Calculate and apply discount
      const subtotal = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);
      const calculatedDiscount = await calculateCouponDiscount(coupon, subtotal, shippingAmount);
      setDiscountAmount(calculatedDiscount);

      toast.success(`Coupon "${coupon.code}" applied! Discount: $${calculatedDiscount.toFixed(2)}`);
    } else {
      setCouponCode('');
      setAppliedCoupon(null);
      setCouponStatus('idle');
      setDiscountAmount(0);
    }
    setCouponSearchOpen(false);
  };

  const handleValidateCoupon = async () => {
    if (!couponCode) return;
    setCouponStatus('checking');
    setAppliedCoupon(null);
    try {
      const res = await api.validateCoupon(couponCode);
      if (res.success && res.data) {
        handleCouponSelect(res.data);
      } else {
        setCouponStatus('invalid');
        setAppliedCoupon(null);
        setDiscountAmount(0);
        toast.error(res.error || 'Invalid or expired coupon code');
      }
    } catch (e: any) {
      setCouponStatus('invalid');
      setAppliedCoupon(null);
      setDiscountAmount(0);
      toast.error(e.message || 'Invalid or expired coupon code');
    }
  };

  // Calculate totals
  const subtotal = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);

  // Recalculate discount when subtotal changes and coupon is applied
  useEffect(() => {
    const recalculateDiscount = async () => {
      if (appliedCoupon && subtotal > 0) {
        const newDiscount = await calculateCouponDiscount(appliedCoupon, subtotal, shippingAmount);
        setDiscountAmount(newDiscount);
      }
    };

    recalculateDiscount();
  }, [subtotal, shippingAmount, appliedCoupon]);

  // Recalculate tax when subtotal changes and tax rate is available
  useEffect(() => {
    if (applicableTaxRate && subtotal > 0) {
      const calculatedTax = subtotal * (applicableTaxRate.rate / 100);
      setTaxAmount(calculatedTax);
    }
  }, [subtotal, applicableTaxRate]);

  const totalAmount = subtotal - discountAmount + shippingAmount + taxAmount;

  const handleSubmit = async () => {
    if (!selectedCustomer) {
      toast.error('Please select a customer');
      return;
    }

    if (!selectedBillingAddress || !selectedShippingAddress) {
      toast.error('Please select billing and shipping addresses');
      return;
    }

    if (orderItems.length === 0) {
      toast.error('Please add at least one item');
      return;
    }

    // Validate inventory availability one final time
    for (const item of orderItems) {
      if (!item.variant?.inventory) {
        toast.error('Invalid product variant');
        return;
      }

      const totalAvailable = item.variant.inventory.reduce((sum, inv) => 
        sum + Math.max(0, inv.quantity - inv.reservedQty), 0);

      if (item.quantity > totalAvailable) {
        toast.error(`Not enough stock available for ${item.variant.product?.name} - ${item.variant.name}`);
        return;
      }
    }

    try {
      setLoading(true);
      
      const orderData = {
        customerId: selectedCustomer.id,
        billingAddressId: selectedBillingAddress.id,
        shippingAddressId: selectedShippingAddress.id,
        items: orderItems.map(item => ({
          variantId: item.variantId,
          quantity: item.quantity,
          unitPrice: item.unitPrice.toString(),
        })),
        discountAmount: discountAmount.toString(),
        shippingAmount: shippingAmount.toString(),
        taxAmount: taxAmount.toString(),
        couponCode: appliedCoupon ? appliedCoupon.code : undefined,
      };

      const response = await api.createOrder(orderData);
      
      if (response.success) {
        // Add note if provided
        if (notes.trim() && response.data) {
          await api.addOrderNote(response.data.id, notes.trim());
        }
        
        toast.success('Order created successfully');
        resetForm();
        onSuccess();
        onOpenChange(false);
      }
      // Show applied coupon details if present
      if (response.data?.appliedCoupon) {
        setAppliedCoupon(response.data.appliedCoupon);
      }
    } catch (error: any) {
      console.error('Failed to create order:', error);
      toast.error(error?.message || 'Failed to create order');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setStep(1);
    setSelectedCustomer(null);
    setSelectedBillingAddress(null);
    setSelectedShippingAddress(null);
    setOrderItems([]);
    setDiscountAmount(0);
    setShippingAmount(0);
    setTaxAmount(0);
    setNotes('');
    setCouponCode('');
    setCouponStatus('idle');
    setAppliedCoupon(null);
    setCouponSearchOpen(false);
    setApplicableTaxRate(null);
    setTaxRateLoading(false);
  };

  const handleClose = () => {
    if (!loading) {
      resetForm();
      onOpenChange(false);
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const canProceedToStep2 = selectedCustomer && selectedBillingAddress && selectedShippingAddress;
  const canProceedToStep3 = orderItems.length > 0;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Order</DialogTitle>
          <DialogDescription>
            Follow the steps to create a new order
          </DialogDescription>
        </DialogHeader>

        <Tabs value={step.toString()} onValueChange={(value) => setStep(parseInt(value))}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="1" className="flex items-center gap-2">
              <User className="w-4 h-4" />
              Customer
            </TabsTrigger>
            <TabsTrigger value="2" disabled={!canProceedToStep2} className="flex items-center gap-2">
              <Package className="w-4 h-4" />
              Products
            </TabsTrigger>
            <TabsTrigger value="3" disabled={!canProceedToStep3} className="flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              Review
            </TabsTrigger>
          </TabsList>

          <TabsContent value="1" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Select Customer</CardTitle>
                <CardDescription>Choose the customer for this order</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="customer">Customer</Label>
                    <Select value={selectedCustomer?.id || ''} onValueChange={handleCustomerSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a customer" />
                      </SelectTrigger>
                      <SelectContent>
                        {customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id}>
                            {customer.firstName} {customer.lastName} - {customer.email}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedCustomer && (
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="billingAddress">Billing Address</Label>
                        <Select 
                          value={selectedBillingAddress?.id || ''} 
                          onValueChange={(value) => {
                            const address = selectedCustomer.addresses?.find(a => a.id === value);
                            setSelectedBillingAddress(address || null);
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select billing address" />
                          </SelectTrigger>
                          <SelectContent>
                            {selectedCustomer.addresses?.map((address) => (
                              <SelectItem key={address.id} value={address.id}>
                                {address.firstName} {address.lastName} - {address.address1}, {address.city}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="shippingAddress">Shipping Address</Label>
                        <Select
                          value={selectedShippingAddress?.id || ''}
                          onValueChange={(value) => {
                            const address = selectedCustomer.addresses?.find(a => a.id === value);
                            setSelectedShippingAddress(address || null);
                            // Fetch applicable tax rate when shipping address changes
                            if (address) {
                              fetchApplicableTaxRate(address);
                            } else {
                              setApplicableTaxRate(null);
                              setTaxAmount(0);
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select shipping address" />
                          </SelectTrigger>
                          <SelectContent>
                            {selectedCustomer.addresses?.map((address) => (
                              <SelectItem key={address.id} value={address.id}>
                                {address.firstName} {address.lastName} - {address.address1}, {address.city}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={() => setStep(2)}
                    disabled={!canProceedToStep2}
                  >
                    Next: Add Products
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="2" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Add Products</CardTitle>
                <CardDescription>Search and add products to the order</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <div className="grid gap-4 max-h-60 overflow-y-auto">
                  {filteredProducts.map((product) => (
                    <div key={product.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{product.name}</h4>
                          <p className="text-sm text-muted-foreground">{product.description}</p>
                        </div>
                        <Badge variant="outline">{product.variants?.length || 0} variants</Badge>
                      </div>
                      
                      {product.variants && product.variants.length > 0 && (
                        <div className="mt-3 space-y-2">
                          {product.variants.map((variant) => {
                            // Debug logging
                            console.log('Rendering variant:', variant.name, 'inventory:', variant.inventory);
                            
                            const totalAvailable = variant.inventory?.reduce((sum, inv) => {
                              const available = Math.max(0, (inv.quantity || 0) - (inv.reservedQty || 0));
                              console.log(`Location ${inv.locationId}: quantity=${inv.quantity}, reservedQty=${inv.reservedQty}, available=${available}`);
                              return sum + available;
                            }, 0) || 0;

                            console.log('Total available for display:', totalAvailable);

                            // Get segment-specific price for display
                            const segmentPrice = selectedCustomer?.customerType && variant.segmentPrices?.find(
                              sp => sp.customerType === selectedCustomer.customerType
                            );

                            const displayPrice = segmentPrice 
                              ? (segmentPrice.salePrice || segmentPrice.regularPrice)
                              : (variant.salePrice || variant.regularPrice);

                            const priceText = segmentPrice 
                              ? `$${displayPrice} (${selectedCustomer.customerType} price)`
                              : `$${displayPrice}`;

                            return (
                              <div key={variant.id} className="flex items-center justify-between p-2 border rounded">
                                <div className="flex-1">
                                  <div className="font-medium">{variant.name}</div>
                                  <div className="text-sm text-muted-foreground">
                                    SKU: {variant.sku} • {priceText}
                                    <span className="ml-2">
                                      {totalAvailable > 0 ? (
                                        <Badge variant="outline" className="ml-2">
                                          {totalAvailable} in stock
                                        </Badge>
                                      ) : (
                                        <Badge variant="destructive" className="ml-2">
                                          Out of stock
                                        </Badge>
                                      )}
                                    </span>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  onClick={() => handleAddOrderItem(variant)}
                                  disabled={totalAvailable <= 0 || orderItems.some(item => item.variantId === variant.id)}
                                >
                                  <Plus className="w-4 h-4 mr-1" />
                                  Add
                                </Button>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setStep(1)}>
                    Back
                  </Button>
                  <Button
                    onClick={() => setStep(3)}
                    disabled={!canProceedToStep3}
                  >
                    Next: Review Order
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="3" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Review Order</CardTitle>
                <CardDescription>Review and finalize the order details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Order Items */}
                <div className="space-y-2">
                  <h4 className="font-medium">Order Items</h4>
                  {orderItems.map((item) => (
                    <div key={item.variantId} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <div className="font-medium">{item.variant?.product?.name} - {item.variant?.name}</div>
                        <div className="text-sm text-muted-foreground">SKU: {item.variant?.sku}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => handleUpdateQuantity(item.variantId, parseInt(e.target.value) || 0)}
                          className="w-16 text-center"
                          min="1"
                        />
                        <span className="text-sm">×</span>
                        <Input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => handleUpdatePrice(item.variantId, parseFloat(e.target.value) || 0)}
                          className="w-20 text-center"
                          min="0"
                          step="0.01"
                        />
                        <span className="text-sm font-medium">${(item.totalPrice || 0).toFixed(2)}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveOrderItem(item.variantId)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Order Summary */}
                <div className="space-y-2">
                  <h4 className="font-medium">Order Summary</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>${subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Discount:</span>
                      <Input
                        type="number"
                        value={discountAmount}
                        onChange={(e) => setDiscountAmount(parseFloat(e.target.value) || 0)}
                        className="w-24 text-right"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Shipping:</span>
                      <Input
                        type="number"
                        value={shippingAmount}
                        onChange={(e) => setShippingAmount(parseFloat(e.target.value) || 0)}
                        className="w-24 text-right"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex flex-col">
                          <span>Tax:</span>
                          {applicableTaxRate && (
                            <span className="text-xs text-muted-foreground">
                              {applicableTaxRate.country}{applicableTaxRate.state ? `, ${applicableTaxRate.state}` : ''} - {applicableTaxRate.rate}% ({applicableTaxRate.type})
                            </span>
                          )}
                          {taxRateLoading && (
                            <span className="text-xs text-muted-foreground">Loading tax rate...</span>
                          )}
                          {!applicableTaxRate && !taxRateLoading && selectedShippingAddress && (
                            <span className="text-xs text-muted-foreground">No tax rate found</span>
                          )}
                        </div>
                        <Input
                          type="number"
                          value={taxAmount}
                          onChange={(e) => setTaxAmount(parseFloat(e.target.value) || 0)}
                          className="w-24 text-right"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                    <div className="flex justify-between font-medium text-lg pt-2 border-t">
                      <span>Total:</span>
                      <span>${totalAmount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                <div className="space-y-2">
                  <Label htmlFor="notes">Order Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Add any notes about this order..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Coupon Code</Label>
                  <div className="flex gap-2">
                    <Popover open={couponSearchOpen} onOpenChange={setCouponSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={couponSearchOpen}
                          className="w-48 justify-between"
                        >
                          {appliedCoupon ? appliedCoupon.code : "Select coupon..."}
                          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80 p-0">
                        <Command>
                          <CommandInput placeholder="Search coupons..." />
                          <CommandEmpty>No coupons found.</CommandEmpty>
                          <CommandGroup>
                            {availableCoupons.map((coupon) => {
                              // Simple discount calculation for display (not async)
                              let discount = 0;
                              const isEligible = !coupon.minOrderAmount || subtotal >= parseFloat(coupon.minOrderAmount.toString());

                              if (isEligible) {
                                switch (coupon.type) {
                                  case 'PERCENTAGE':
                                    discount = subtotal * (parseFloat(coupon.value.toString()) / 100);
                                    break;
                                  case 'FIXED_AMOUNT':
                                    discount = parseFloat(coupon.value.toString());
                                    break;
                                  case 'FREE_SHIPPING':
                                    discount = shippingAmount;
                                    break;
                                  case 'BOGO':
                                    discount = subtotal * 0.25; // Estimated discount for display
                                    break;
                                  case 'VOLUME_DISCOUNT':
                                    discount = subtotal * 0.1; // Estimated discount for display
                                    break;
                                }

                                if (coupon.maxDiscount) {
                                  discount = Math.min(discount, parseFloat(coupon.maxDiscount.toString()));
                                }
                                discount = Math.min(discount, subtotal);
                              }

                              return (
                                <CommandItem
                                  key={coupon.id}
                                  value={coupon.code}
                                  onSelect={() => handleCouponSelect(coupon)}
                                  disabled={!isEligible}
                                  className={!isEligible ? 'opacity-50' : ''}
                                >
                                  <Check
                                    className={`mr-2 h-4 w-4 ${
                                      appliedCoupon?.id === coupon.id ? "opacity-100" : "opacity-0"
                                    }`}
                                  />
                                  <div className="flex-1">
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium">{coupon.code}</span>
                                      <span className="text-sm text-green-600">
                                        {isEligible ? (
                                          coupon.type === 'BOGO' ? (
                                            <div className="text-right">
                                              <div>{`~$${discount.toFixed(2)}`}</div>
                                              <div className="text-xs text-gray-500">
                                                {(() => {
                                                  const totalQty = orderItems.reduce((sum, item) => sum + item.quantity, 0);
                                                  const buyQty = (coupon as any).buyQuantity || 2;
                                                  const getQty = (coupon as any).getQuantity || 1;
                                                  const freeItems = Math.floor(totalQty / buyQty) * getQty;
                                                  return `${freeItems} free item${freeItems !== 1 ? 's' : ''}`;
                                                })()}
                                              </div>
                                            </div>
                                          ) : coupon.type === 'VOLUME_DISCOUNT' ? (
                                            `~$${discount.toFixed(2)}`
                                          ) : (
                                            `-$${discount.toFixed(2)}`
                                          )
                                        ) : 'Not eligible'}
                                      </span>
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                      {coupon.name}
                                      {coupon.minOrderAmount && (
                                        <span className="ml-2">
                                          (Min: ${parseFloat(coupon.minOrderAmount.toString()).toFixed(2)})
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </CommandItem>
                              );
                            })}
                          </CommandGroup>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    <div className="flex-1">
                      <Input
                        ref={couponInputRef}
                        value={couponCode}
                        onChange={e => setCouponCode(e.target.value)}
                        placeholder="Or enter coupon code"
                        className="w-full"
                      />
                    </div>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleValidateCoupon}
                      disabled={couponStatus === 'checking' || !couponCode}
                    >
                      {couponStatus === 'checking' ? 'Checking...' : 'Apply'}
                    </Button>

                    {appliedCoupon && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => handleCouponSelect(null)}
                        size="sm"
                      >
                        Clear
                      </Button>
                    )}
                  </div>

                  {couponStatus === 'valid' && appliedCoupon && (
                    <div className="text-green-600 text-sm bg-green-50 p-2 rounded">
                      <div className="font-medium">✓ Coupon applied: {appliedCoupon.code}</div>
                      <div>{appliedCoupon.name}</div>
                      <div>Discount: ${discountAmount.toFixed(2)}</div>

                      {appliedCoupon.type === 'BOGO' && (
                        <div className="mt-1 text-blue-600">
                          {(() => {
                            const totalQty = orderItems.reduce((sum, item) => sum + item.quantity, 0);
                            const buyQty = (appliedCoupon as any).buyQuantity || 2;
                            const getQty = (appliedCoupon as any).getQuantity || 1;
                            const freeItems = Math.floor(totalQty / buyQty) * getQty;
                            const nextFreeAt = Math.ceil(totalQty / buyQty) * buyQty;

                            return (
                              <div>
                                <div>🎁 {freeItems} free item{freeItems !== 1 ? 's' : ''} earned</div>
                                {freeItems === 0 && (
                                  <div className="text-orange-600">
                                    Add {buyQty - totalQty} more item{buyQty - totalQty !== 1 ? 's' : ''} to get {getQty} free
                                  </div>
                                )}
                                {freeItems > 0 && totalQty < nextFreeAt && (
                                  <div className="text-gray-600">
                                    Add {nextFreeAt - totalQty} more for next free item
                                  </div>
                                )}
                              </div>
                            );
                          })()}
                        </div>
                      )}

                      {appliedCoupon.minOrderAmount && subtotal < parseFloat(appliedCoupon.minOrderAmount.toString()) && (
                        <div className="text-orange-600">
                          Add ${(parseFloat(appliedCoupon.minOrderAmount.toString()) - subtotal).toFixed(2)} more to qualify
                        </div>
                      )}
                    </div>
                  )}

                  {couponStatus === 'invalid' && (
                    <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
                      Invalid or expired coupon code
                    </div>
                  )}
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setStep(2)}>
                    Back
                  </Button>
                  <Button onClick={handleSubmit} disabled={loading}>
                    {loading ? 'Creating Order...' : 'Create Order'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}