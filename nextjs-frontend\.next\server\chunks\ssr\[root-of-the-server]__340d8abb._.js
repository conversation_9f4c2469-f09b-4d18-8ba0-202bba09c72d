module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/components/theme-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-ssr] (ecmascript)");
"use client";
;
;
function ThemeProvider({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/theme-provider.tsx",
        lineNumber: 15,
        columnNumber: 12
    }, this);
}
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TOKEN_KEY": (()=>TOKEN_KEY),
    "api": (()=>api),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "getStatusColor": (()=>getStatusColor),
    "getToken": (()=>getToken),
    "removeToken": (()=>removeToken),
    "setToken": (()=>setToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
;
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:5001/api") || "http://localhost:5000/api";
const TOKEN_KEY = "auth_token";
const getToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return null;
    "TURBOPACK unreachable";
};
const setToken = (token)=>{
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
};
const removeToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
};
// API client class
class ApiClient {
    baseURL;
    constructor(baseURL){
        this.baseURL = baseURL;
    }
    getHeaders() {
        const headers = {
            "Content-Type": "application/json"
        };
        const token = getToken();
        if (token) {
            headers.Authorization = `Bearer ${token}`;
            console.log('[API] Sending Authorization header:', headers.Authorization);
        } else {
            console.log('[API] No token found for Authorization header');
        }
        return headers;
    }
    async request(endpoint, options = {}) {
        try {
            const url = `${this.baseURL}${endpoint}`;
            const config = {
                headers: this.getHeaders(),
                ...options
            };
            const response = await fetch(url, config);
            const data = await response.json();
            // Check for 401 Unauthorized first
            if (response.status === 401) {
                removeToken();
                // Don't redirect here - let the ProtectedRoute handle it
                return {
                    success: false,
                    error: "Authentication required"
                };
            }
            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
            }
            return data;
        } catch (error) {
            console.error("API request failed:", error);
            if (error instanceof Error) {
                // Show error toast
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error.message);
                return {
                    success: false,
                    error: error.message
                };
            }
            return {
                success: false,
                error: "An unexpected error occurred"
            };
        }
    }
    // Add generic HTTP methods
    async get(endpoint) {
        return this.request(endpoint, {
            method: "GET"
        });
    }
    async post(endpoint, body) {
        return this.request(endpoint, {
            method: "POST",
            headers: {
                ...this.getHeaders(),
                "Content-Type": "application/json"
            },
            body: body ? JSON.stringify(body) : undefined
        });
    }
    async put(endpoint, body) {
        return this.request(endpoint, {
            method: "PUT",
            headers: {
                ...this.getHeaders(),
                "Content-Type": "application/json"
            },
            body: body ? JSON.stringify(body) : undefined
        });
    }
    async delete(endpoint) {
        return this.request(endpoint, {
            method: "DELETE",
            headers: this.getHeaders()
        });
    }
    // Authentication endpoints
    async login(email, password) {
        return this.request("/auth/login", {
            method: "POST",
            body: JSON.stringify({
                email,
                password
            })
        });
    }
    async register(userData) {
        return this.request("/auth/register", {
            method: "POST",
            body: JSON.stringify(userData)
        });
    }
    async getProfile() {
        return this.request("/auth/profile");
    }
    async updateProfile(userData) {
        return this.request("/auth/profile", {
            method: "PUT",
            body: JSON.stringify(userData)
        });
    }
    async changePassword(currentPassword, newPassword) {
        return this.request("/auth/change-password", {
            method: "PUT",
            body: JSON.stringify({
                currentPassword,
                newPassword
            })
        });
    }
    async logout() {
        return this.request("/auth/logout", {
            method: "POST"
        });
    }
    // User management endpoints
    async getUsers(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            Object.entries(params).forEach(([key, value])=>{
                if (value !== undefined) {
                    searchParams.append(key, value.toString());
                }
            });
        }
        return this.request(`/users?${searchParams.toString()}`);
    }
    async getUser(id) {
        return this.request(`/users/${id}`);
    }
    async createUser(userData) {
        return this.request("/users", {
            method: "POST",
            body: JSON.stringify(userData)
        });
    }
    async updateUser(id, userData) {
        return this.request(`/users/${id}`, {
            method: "PUT",
            body: JSON.stringify(userData)
        });
    }
    async deleteUser(id) {
        return this.request(`/users/${id}`, {
            method: "DELETE"
        });
    }
    async resetUserPassword(id, newPassword) {
        return this.request(`/users/${id}/reset-password`, {
            method: "POST",
            body: JSON.stringify({
                newPassword
            })
        });
    }
    async updateUserPermissions(id, permissions) {
        return this.request(`/users/${id}/permissions`, {
            method: "PUT",
            body: JSON.stringify({
                permissions
            })
        });
    }
    // Customer management endpoints
    async getCustomers(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            Object.entries(params).forEach(([key, value])=>{
                if (value !== undefined) {
                    searchParams.append(key, value.toString());
                }
            });
        }
        const response = await this.request(`/customers?${searchParams.toString()}`);
        if (response.success && response.data) {
            return {
                success: true,
                data: {
                    customers: response.data.customers,
                    pagination: response.data.pagination
                }
            };
        }
        return response;
    }
    async getCustomer(id) {
        return this.request(`/customers/${id}`);
    }
    async createCustomer(customerData) {
        return this.request("/customers", {
            method: "POST",
            body: JSON.stringify(customerData)
        });
    }
    async updateCustomer(id, customerData) {
        return this.request(`/customers/${id}`, {
            method: "PUT",
            body: JSON.stringify(customerData)
        });
    }
    async deleteCustomer(id) {
        return this.request(`/customers/${id}`, {
            method: "DELETE"
        });
    }
    // Product management endpoints
    async getProducts(params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append("page", params.page.toString());
        if (params?.limit) queryParams.append("limit", params.limit.toString());
        if (params?.status) queryParams.append("status", params.status);
        if (params?.search) queryParams.append("search", params.search);
        if (params?.category) queryParams.append("category", params.category);
        if (params?.include?.variants?.include?.inventory) {
            queryParams.append("include", "variants.inventory");
        }
        const response = await this.request(`/products?${queryParams}`);
        if (response.success && response.data) {
            return {
                success: true,
                data: {
                    products: response.data.products,
                    pagination: response.data.pagination
                }
            };
        }
        return response;
    }
    async getProduct(id) {
        return this.request(`/products/${id}`);
    }
    async createProduct(productData) {
        return this.request("/products", {
            method: "POST",
            body: JSON.stringify(productData)
        });
    }
    async updateProduct(id, productData) {
        return this.request(`/products/${id}`, {
            method: "PUT",
            body: JSON.stringify(productData)
        });
    }
    async deleteProduct(id) {
        return this.request(`/products/${id}`, {
            method: "DELETE"
        });
    }
    // Order management endpoints
    async getOrders(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            Object.entries(params).forEach(([key, value])=>{
                if (value !== undefined) {
                    searchParams.append(key, value.toString());
                }
            });
        }
        const response = await this.request(`/orders?${searchParams.toString()}`);
        if (response.success && response.data) {
            return {
                success: true,
                data: {
                    orders: response.data.orders,
                    pagination: response.data.pagination
                }
            };
        }
        return response;
    }
    async getOrder(id) {
        return this.request(`/orders/${id}`);
    }
    async createOrder(orderData) {
        return this.request("/orders", {
            method: "POST",
            body: JSON.stringify(orderData)
        });
    }
    async updateOrder(id, orderData) {
        return this.request(`/orders/${id}`, {
            method: "PUT",
            body: JSON.stringify(orderData)
        });
    }
    async updateOrderStatus(id, status, note) {
        return this.request(`/orders/${id}/status`, {
            method: "PATCH",
            body: JSON.stringify({
                status,
                note
            })
        });
    }
    async addOrderNote(id, note, isInternal = true) {
        return this.request(`/orders/${id}/notes`, {
            method: "POST",
            body: JSON.stringify({
                note,
                isInternal
            })
        });
    }
    async getOrderNotes(id) {
        return this.request(`/orders/${id}/notes`);
    }
    async deleteOrder(id) {
        return this.request(`/orders/${id}`, {
            method: "DELETE"
        });
    }
    // File upload
    async uploadFile(file) {
        const formData = new FormData();
        formData.append("file", file);
        const token = getToken();
        const headers = {};
        if (token) {
            headers.Authorization = `Bearer ${token}`;
        }
        try {
            const response = await fetch(`${this.baseURL}/upload`, {
                method: "POST",
                headers,
                body: formData
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
            }
            return data;
        } catch (error) {
            console.error("File upload failed:", error);
            if (error instanceof Error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error.message);
                return {
                    success: false,
                    error: error.message
                };
            }
            return {
                success: false,
                error: "File upload failed"
            };
        }
    }
    async createAddress(customerId, addressData) {
        return this.request(`/customers/${customerId}/addresses`, {
            method: "POST",
            body: JSON.stringify(addressData)
        });
    }
    async updateAddress(customerId, addressId, addressData) {
        return this.request(`/customers/${customerId}/addresses/${addressId}`, {
            method: "PUT",
            body: JSON.stringify(addressData)
        });
    }
    async deleteAddress(customerId, addressId) {
        return this.request(`/customers/${customerId}/addresses/${addressId}`, {
            method: "DELETE"
        });
    }
    // Inventory Management
    async getLocations() {
        return this.request("/locations");
    }
    async getInventory(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            if (params.page) searchParams.append("page", params.page.toString());
            if (params.limit) searchParams.append("limit", params.limit.toString());
            if (params.search) searchParams.append("search", params.search);
            if (params.locationId) searchParams.append("locationId", params.locationId);
            if (params.lowStock) searchParams.append("lowStock", params.lowStock.toString());
        }
        const queryString = searchParams.toString();
        return this.request(`/inventory${queryString ? `?${queryString}` : ""}`);
    }
    async updateInventory(id, data) {
        return this.request(`/inventory/${id}`, {
            method: "PUT",
            body: JSON.stringify(data)
        });
    }
    async createInventoryMovement(data) {
        return this.request("/inventory/movement", {
            method: "POST",
            body: JSON.stringify(data)
        });
    }
    // Category Management
    async getCategories(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            if (params.page) searchParams.append("page", params.page.toString());
            if (params.limit) searchParams.append("limit", params.limit.toString());
            if (params.search) searchParams.append("search", params.search);
        }
        const queryString = searchParams.toString();
        return this.request(`/categories${queryString ? `?${queryString}` : ""}`);
    }
    async createCategory(data) {
        return this.request("/categories", {
            method: "POST",
            body: JSON.stringify(data)
        });
    }
    async updateCategory(id, data) {
        return this.request(`/categories/${id}`, {
            method: "PUT",
            body: JSON.stringify(data)
        });
    }
    async deleteCategory(id) {
        return this.request(`/categories/${id}`, {
            method: "DELETE"
        });
    }
    async getCollections(params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append("page", params.page.toString());
        if (params?.limit) queryParams.append("limit", params.limit.toString());
        if (params?.search) queryParams.append("search", params.search);
        if (typeof params?.isActive === "boolean") queryParams.append("isActive", params.isActive.toString());
        return this.request(`/collections?${queryParams.toString()}`);
    }
    async getCollection(id) {
        return this.request(`/collections/${id}`);
    }
    async createCollection(collectionData) {
        return this.request("/collections", {
            method: "POST",
            body: JSON.stringify(collectionData)
        });
    }
    async updateCollection(id, collectionData) {
        return this.request(`/collections/${id}`, {
            method: "PATCH",
            body: JSON.stringify(collectionData)
        });
    }
    async deleteCollection(id) {
        return this.request(`/collections/${id}`, {
            method: "DELETE"
        });
    }
    async updateCollectionProducts(id, productIds) {
        return this.request(`/collections/${id}/products`, {
            method: "PUT",
            body: JSON.stringify({
                productIds
            })
        });
    }
    async reorderCollectionProducts(id, productOrders) {
        return this.request(`/collections/${id}/products/reorder`, {
            method: "PATCH",
            body: JSON.stringify({
                productOrders
            })
        });
    }
    async createProductVariant(productId, variantData) {
        return this.request(`/products/${productId}/variants`, {
            method: "POST",
            body: JSON.stringify(variantData)
        });
    }
    async updateProductVariant(productId, variantId, variantData) {
        return this.request(`/products/${productId}/variants/${variantId}`, {
            method: "PUT",
            body: JSON.stringify(variantData)
        });
    }
    async deleteProductVariant(productId, variantId) {
        return this.request(`/products/${productId}/variants/${variantId}`, {
            method: "DELETE"
        });
    }
    // Inventory Location endpoints
    async createLocation(data) {
        return this.post('/locations', data);
    }
    async updateLocation(id, data) {
        return this.put(`/locations/${id}`, data);
    }
    async deleteLocation(id) {
        return this.delete(`/locations/${id}`);
    }
    async initiateOrderRefund(orderId, amount, reason) {
        const token = getToken();
        const response = await fetch(`${this.baseURL}/orders/${orderId}/refund`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            },
            body: JSON.stringify({
                amount,
                reason
            })
        });
        return response.json();
    }
    async updateRefundStatus(refundId, status) {
        const token = getToken();
        const response = await fetch(`${this.baseURL}/orders/refunds/${refundId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                ...token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            },
            body: JSON.stringify({
                status
            })
        });
        return response.json();
    }
    // Transaction management endpoints
    async getTransactions(params) {
        const queryParams = new URLSearchParams();
        if (params?.orderId) queryParams.append('orderId', params.orderId);
        if (params?.paymentStatus) queryParams.append('paymentStatus', params.paymentStatus);
        if (params?.paymentGatewayName) queryParams.append('paymentGatewayName', params.paymentGatewayName);
        return this.get(`/transactions${queryParams.toString() ? `?${queryParams}` : ''}`);
    }
    async getTransaction(id) {
        return this.get(`/transactions/${id}`);
    }
    async createTransaction(data) {
        return this.post('/transactions', data);
    }
    // Promotion/Coupon management endpoints
    async getPromotions(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            Object.entries(params).forEach(([key, value])=>{
                if (value !== undefined) {
                    searchParams.append(key, value.toString());
                }
            });
        }
        return this.request(`/promotions?${searchParams.toString()}`);
    }
    async getPromotion(id) {
        return this.get(`/promotions/${id}`);
    }
    // Tax Rates
    async getTaxRates(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            if (params.country) searchParams.append('country', params.country);
            if (params.state) searchParams.append('state', params.state);
        }
        return this.request(`/tax-rates?${searchParams.toString()}`);
    }
    async getApplicableTaxRate(country, state) {
        const searchParams = new URLSearchParams();
        searchParams.append('country', country);
        if (state) searchParams.append('state', state);
        return this.request(`/tax-rates/applicable?${searchParams.toString()}`);
    }
    // Shipping Management
    async getShipments(params) {
        const searchParams = new URLSearchParams();
        if (params) {
            if (params.page) searchParams.append('page', params.page.toString());
            if (params.limit) searchParams.append('limit', params.limit.toString());
            if (params.orderId) searchParams.append('orderId', params.orderId);
            if (params.status) searchParams.append('status', params.status);
        }
        return this.request(`/shipping?${searchParams.toString()}`);
    }
    async getShipment(id) {
        return this.get(`/shipping/${id}`);
    }
    async getOrderShipments(orderId) {
        return this.get(`/shipping/order/${orderId}`);
    }
    async createShipment(data) {
        return this.post('/shipping', data);
    }
    async updateShipmentTracking(id, data) {
        return this.put(`/shipping/${id}/tracking`, data);
    }
    async deleteShipment(id) {
        return this.delete(`/shipping/${id}`);
    }
    // Product Relations Management
    async addProductRelation(productId, relatedProductId, type) {
        return this.post(`/products/${productId}/relations`, {
            relatedProductId,
            type
        });
    }
    async removeProductRelation(productId, relationId) {
        return this.delete(`/products/${productId}/relations/${relationId}`);
    }
    async getProductRelations(productId) {
        return this.get(`/products/${productId}/relations`);
    }
    // Product Reviews Management
    async approveReview(reviewId) {
        return this.put(`/reviews/${reviewId}/approve`, {});
    }
    async deleteReview(reviewId) {
        return this.delete(`/reviews/${reviewId}`);
    }
    async getProductReviews(productId) {
        return this.get(`/products/${productId}/reviews`);
    }
    // Inventory Batch Management
    async getInventoryBatches(inventoryId) {
        return this.get(`/inventory-batches?inventoryId=${inventoryId}`);
    }
    async createInventoryBatch(data) {
        return this.post('/inventory-batches', data);
    }
    async updateInventoryBatch(id, data) {
        return this.put(`/inventory-batches/${id}`, data);
    }
    async deleteInventoryBatch(id) {
        return this.delete(`/inventory-batches/${id}`);
    }
    async getExpiringBatches(days = 30) {
        return this.get(`/inventory-batches/expiring?days=${days}`);
    }
    async getExpiredBatches() {
        return this.get('/inventory-batches/expired');
    }
    async validateCoupon(code) {
        return this.get(`/promotions/code/${code}`);
    }
    async calculatePromotionDiscount(data) {
        return this.post('/promotions/calculate-discount', data);
    }
    async createPromotion(data) {
        return this.post('/promotions', data);
    }
    async updatePromotion(id, data) {
        return this.put(`/promotions/${id}`, data);
    }
    async deletePromotion(id) {
        return this.delete(`/promotions/${id}`);
    }
    async useCoupon(code) {
        return this.post(`/promotions/use/${code}`);
    }
}
const api = new ApiClient(API_BASE_URL);
const formatCurrency = (amount)=>{
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD"
    }).format(amount);
};
const formatDate = (date)=>{
    return new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
    }).format(new Date(date));
};
const getStatusColor = (status)=>{
    const statusColors = {
        PENDING: "bg-yellow-100 text-yellow-800",
        PROCESSING: "bg-blue-100 text-blue-800",
        SHIPPED: "bg-purple-100 text-purple-800",
        DELIVERED: "bg-green-100 text-green-800",
        CANCELLED: "bg-red-100 text-red-800",
        REFUNDED: "bg-gray-100 text-gray-800",
        ON_HOLD: "bg-orange-100 text-orange-800",
        ACTIVE: "bg-green-100 text-green-800",
        INACTIVE: "bg-gray-100 text-gray-800",
        DRAFT: "bg-gray-100 text-gray-800",
        ARCHIVED: "bg-red-100 text-red-800"
    };
    return statusColors[status] || "bg-gray-100 text-gray-800";
};
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/contexts/auth-context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "ProtectedRoute": (()=>ProtectedRoute),
    "useAuth": (()=>useAuth),
    "usePermissions": (()=>usePermissions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const isAuthenticated = !!user;
    // Initialize auth state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeAuth = async ()=>{
            const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])();
            if (token) {
                try {
                    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].getProfile();
                    if (response.success && response.data) {
                        setUser(response.data);
                    } else {
                        // Invalid token, remove it
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["removeToken"])();
                    }
                } catch (error) {
                    console.error('Failed to initialize auth:', error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["removeToken"])();
                }
            }
            setIsLoading(false);
        };
        initializeAuth();
    }, []);
    const login = async (email, password)=>{
        try {
            setIsLoading(true);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].login(email, password);
            if (response.success && response.data) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setToken"])(response.data.token);
                console.log('[Auth] Token set after login:', response.data.token);
                setUser(response.data.user);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Login successful');
                return true;
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(response.error || 'Login failed');
                return false;
            }
        } catch (error) {
            console.error('Login error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Login failed');
            return false;
        } finally{
            setIsLoading(false);
        }
    };
    const register = async (userData)=>{
        try {
            setIsLoading(true);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].register(userData);
            if (response.success && response.data) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setToken"])(response.data.token);
                setUser(response.data.user);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Registration successful');
                return true;
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(response.error || 'Registration failed');
                return false;
            }
        } catch (error) {
            console.error('Registration error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Registration failed');
            return false;
        } finally{
            setIsLoading(false);
        }
    };
    const logout = ()=>{
        try {
            // Call logout API (but don't wait for it)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].logout().catch(console.error);
            // Clear local state
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["removeToken"])();
            setUser(null);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Logged out successfully');
            router.push('/login');
        } catch (error) {
            console.error('Logout error:', error);
        }
    };
    const refreshUser = async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].getProfile();
            if (response.success && response.data) {
                setUser(response.data);
            }
        } catch (error) {
            console.error('Failed to refresh user:', error);
        }
    };
    const hasPermission = (module, action)=>{
        if (!user) return false;
        // Admins have all permissions
        if (user.role === 'ADMIN') return true;
        // Check specific permissions
        if (user.permissions) {
            return user.permissions.some((permission)=>permission.module === module && permission.action === action && permission.granted);
        }
        return false;
    };
    const hasRole = (roles)=>{
        if (!user) return false;
        const roleArray = Array.isArray(roles) ? roles : [
            roles
        ];
        return roleArray.includes(user.role);
    };
    const value = {
        user,
        isLoading,
        isAuthenticated,
        login,
        logout,
        register,
        refreshUser,
        hasPermission,
        hasRole
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/auth-context.tsx",
        lineNumber: 192,
        columnNumber: 5
    }, this);
};
const ProtectedRoute = ({ children, requiredRoles, requiredPermissions, fallback })=>{
    const { user, isLoading, isAuthenticated, hasRole, hasPermission } = useAuth();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Only redirect if we're not loading and the user is definitely not authenticated
        if (!isLoading && !isAuthenticated) {
            router.push('/login');
        }
    }, [
        isLoading,
        isAuthenticated,
        router
    ]);
    // Show loading state while checking authentication
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"
            }, void 0, false, {
                fileName: "[project]/src/contexts/auth-context.tsx",
                lineNumber: 226,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/contexts/auth-context.tsx",
            lineNumber: 225,
            columnNumber: 7
        }, this);
    }
    // If not authenticated and not loading, show fallback or null
    if (!isAuthenticated || !user) {
        return fallback || null;
    }
    // Check role requirements
    if (requiredRoles && !hasRole(requiredRoles)) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold text-gray-900 mb-2",
                        children: "Access Denied"
                    }, void 0, false, {
                        fileName: "[project]/src/contexts/auth-context.tsx",
                        lineNumber: 241,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "You don't have the required role to access this page."
                    }, void 0, false, {
                        fileName: "[project]/src/contexts/auth-context.tsx",
                        lineNumber: 242,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/contexts/auth-context.tsx",
                lineNumber: 240,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/contexts/auth-context.tsx",
            lineNumber: 239,
            columnNumber: 7
        }, this);
    }
    // Check permission requirements
    if (requiredPermissions) {
        const hasAllPermissions = requiredPermissions.every(({ module, action })=>hasPermission(module, action));
        if (!hasAllPermissions) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center min-h-screen",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-bold text-gray-900 mb-2",
                            children: "Access Denied"
                        }, void 0, false, {
                            fileName: "[project]/src/contexts/auth-context.tsx",
                            lineNumber: 258,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600",
                            children: "You don't have the required permissions to access this page."
                        }, void 0, false, {
                            fileName: "[project]/src/contexts/auth-context.tsx",
                            lineNumber: 259,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/contexts/auth-context.tsx",
                    lineNumber: 257,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/contexts/auth-context.tsx",
                lineNumber: 256,
                columnNumber: 9
            }, this);
        }
    }
    // All checks passed, render the protected content
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
};
const usePermissions = ()=>{
    const { hasPermission, hasRole, user } = useAuth();
    return {
        hasPermission,
        hasRole,
        user,
        canCreate: (module)=>hasPermission(module, 'CREATE'),
        canRead: (module)=>hasPermission(module, 'READ'),
        canUpdate: (module)=>hasPermission(module, 'UPDATE'),
        canDelete: (module)=>hasPermission(module, 'DELETE'),
        isAdmin: ()=>hasRole('ADMIN'),
        isManager: ()=>hasRole([
                'ADMIN',
                'MANAGER'
            ]),
        isStaff: ()=>hasRole([
                'ADMIN',
                'MANAGER',
                'STAFF'
            ])
    };
};
}}),
"[project]/src/components/ui/sonner.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
const Toaster = ({ ...props })=>{
    const { theme = "system" } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            "--normal-bg": "var(--popover)",
            "--normal-text": "var(--popover-foreground)",
            "--normal-border": "var(--border)"
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__340d8abb._.js.map