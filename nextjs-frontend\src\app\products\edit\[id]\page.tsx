'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/dashboard-layout';
import { ProtectedRoute } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Loader2, Plus, X, Upload, Package, ArrowLeft, Edit, Trash2, Star, Check, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { api, Product, ProductVariant } from '@/lib/api';

interface ProductFormData {
  name: string;
  description: string;
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
  categories: string[];
  tags: string[];
  images: { url: string; altText: string; sortOrder: number }[];
  seoTitle?: string;
  seoDescription?: string;
  seoSlug?: string;
}

interface FormErrors {
  name?: string;
  description?: string;
}

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    status: 'DRAFT',
    categories: [],
    tags: [],
    images: [],
    seoTitle: '',
    seoDescription: '',
    seoSlug: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newCategory, setNewCategory] = useState('');
  const [newTag, setNewTag] = useState('');
  const [uploadingImage, setUploadingImage] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Variants state
  const [variants, setVariants] = useState<ProductVariant[]>([]);
  const [showVariantForm, setShowVariantForm] = useState(false);
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null);
  const [variantFormData, setVariantFormData] = useState({
    sku: '',
    name: '',
    description: '',
    regularPrice: '',
    salePrice: '',
    weight: '',
    hsn: '',
    isActive: true,
    seoTitle: '',
    seoDescription: '',
    seoSlug: '',
    variantOptions: [] as { name: string; value: string }[],
  });
  const [variantErrors, setVariantErrors] = useState<any>({});

  // Related products state
  const [relatedProducts, setRelatedProducts] = useState<any[]>([]);
  const [upsellProducts, setUpsellProducts] = useState<any[]>([]);
  const [crossSellProducts, setCrossSellProducts] = useState<any[]>([]);
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [productSearchQuery, setProductSearchQuery] = useState('');

  // Reviews state
  const [reviews, setReviews] = useState<any[]>([]);

  useEffect(() => {
    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      const response = await api.getProduct(productId);
      if (response.success && response.data) {
        const productData = response.data;
        setProduct(productData);
        setFormData({
          name: productData.name || '',
          description: productData.description || '',
          status: productData.status || 'DRAFT',
          categories: productData.categories?.map(cat => cat.name) || [],
          tags: productData.tags?.map(tag => tag.tag) || [],
          images: productData.images?.map(img => ({
            url: img.url,
            altText: img.altText || '',
            sortOrder: img.sortOrder
          })) || [],
          seoTitle: productData.seoTitle || '',
          seoDescription: productData.seoDescription || '',
          seoSlug: productData.seoSlug || '',
        });

        // Set variants, related products, and reviews
        setVariants(productData.variants || []);

        // Separate related products by type
        const relations = (productData as any).relatedProducts || [];
        setRelatedProducts(relations.filter((r: any) => r.type === 'RELATED'));
        setUpsellProducts(relations.filter((r: any) => r.type === 'UPSELL'));
        setCrossSellProducts(relations.filter((r: any) => r.type === 'CROSS_SELL'));

        setReviews((productData as any).reviews || []);
      } else {
        toast.error('Product not found');
        router.push('/products');
      }
    } catch (error) {
      console.error('Failed to fetch product:', error);
      toast.error('Failed to load product');
      router.push('/products');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Product description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!product || !validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const productData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        status: formData.status,
        categories: formData.categories,
        tags: formData.tags,
        images: formData.images,
        seoTitle: formData.seoTitle?.trim() || undefined,
        seoDescription: formData.seoDescription?.trim() || undefined,
        seoSlug: formData.seoSlug?.trim() || undefined,
      };

      const response = await api.updateProduct(product.id, productData);

      if (response.success) {
        toast.success('Product updated successfully');
        router.push('/products');
      } else {
        toast.error(response.error || 'Failed to update product');
      }
    } catch (error) {
      console.error('Failed to update product:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addCategory = () => {
    if (newCategory.trim() && !formData.categories.includes(newCategory.trim())) {
      setFormData(prev => ({
        ...prev,
        categories: [...prev.categories, newCategory.trim()],
      }));
      setNewCategory('');
    }
  };

  const removeCategory = (category: string) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.filter(c => c !== category),
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag),
    }));
  };

  const handleImageUpload = async (file: File) => {
    setUploadingImage(true);
    try {
      // Simulate image upload - replace with actual upload logic
      const imageUrl = URL.createObjectURL(file);
      const newImage = {
        url: imageUrl,
        altText: file.name,
        sortOrder: formData.images.length,
      };
      
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, newImage],
      }));
      
      toast.success('Image uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload image');
    } finally {
      setUploadingImage(false);
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files[0]);
    }
  }, []);

  // Related products functions
  const searchProducts = async (query: string) => {
    if (!query.trim()) {
      setAvailableProducts([]);
      return;
    }

    try {
      const response = await api.getProducts({ search: query, limit: 10 });
      if (response.success) {
        // Filter out current product and already related products
        const filtered = response.data.data.filter((p: Product) =>
          p.id !== productId &&
          !relatedProducts.some(rp => rp.relatedProduct.id === p.id) &&
          !upsellProducts.some(up => up.relatedProduct.id === p.id) &&
          !crossSellProducts.some(cp => cp.relatedProduct.id === p.id)
        );
        setAvailableProducts(filtered);
      }
    } catch (error) {
      console.error('Failed to search products:', error);
    }
  };

  const addRelatedProduct = async (relatedProductId: string, type: 'RELATED' | 'UPSELL' | 'CROSS_SELL') => {
    try {
      const response = await api.addProductRelation(productId, relatedProductId, type);
      if (response.success) {
        toast.success(`${type.toLowerCase()} product added successfully`);
        // Refresh the product data to get updated relations
        fetchProduct();
        setProductSearchQuery('');
        setAvailableProducts([]);
      } else {
        toast.error('Failed to add related product');
      }
    } catch (error) {
      toast.error('Failed to add related product');
    }
  };

  const removeRelatedProduct = async (relationId: string, type: string) => {
    try {
      const response = await api.removeProductRelation(productId, relationId);
      if (response.success) {
        toast.success(`${type.toLowerCase()} product removed successfully`);
        // Refresh the product data to get updated relations
        fetchProduct();
      } else {
        toast.error('Failed to remove related product');
      }
    } catch (error) {
      toast.error('Failed to remove related product');
    }
  };

  // Review management functions
  const approveReview = async (reviewId: string) => {
    try {
      const response = await api.approveReview(reviewId);
      if (response.success) {
        toast.success('Review approved successfully');
        fetchProduct(); // Refresh to get updated review status
      } else {
        toast.error('Failed to approve review');
      }
    } catch (error) {
      toast.error('Failed to approve review');
    }
  };

  const deleteReview = async (reviewId: string) => {
    try {
      const response = await api.deleteReview(reviewId);
      if (response.success) {
        toast.success('Review deleted successfully');
        fetchProduct(); // Refresh to get updated reviews
      } else {
        toast.error('Failed to delete review');
      }
    } catch (error) {
      toast.error('Failed to delete review');
    }
  };

  // Variant management functions
  const openVariantForm = (variant?: ProductVariant) => {
    if (variant) {
      setEditingVariant(variant);
      setVariantFormData({
        sku: variant.sku,
        name: variant.name,
        description: variant.description || '',
        regularPrice: variant.regularPrice.toString(),
        salePrice: variant.salePrice?.toString() || '',
        weight: variant.weight?.toString() || '',
        hsn: variant.hsn || '',
        isActive: variant.isActive,
        seoTitle: variant.seoTitle || '',
        seoDescription: variant.seoDescription || '',
        seoSlug: variant.seoSlug || '',
        variantOptions: variant.variantOptions || [],
      });
    } else {
      setEditingVariant(null);
      setVariantFormData({
        sku: '',
        name: '',
        description: '',
        regularPrice: '',
        salePrice: '',
        weight: '',
        hsn: '',
        isActive: true,
        seoTitle: '',
        seoDescription: '',
        seoSlug: '',
        variantOptions: [],
      });
    }
    setVariantErrors({});
    setShowVariantForm(true);
  };

  const closeVariantForm = () => {
    setShowVariantForm(false);
    setEditingVariant(null);
    setVariantFormData({
      sku: '',
      name: '',
      description: '',
      regularPrice: '',
      salePrice: '',
      weight: '',
      hsn: '',
      isActive: true,
      seoTitle: '',
      seoDescription: '',
      seoSlug: '',
      variantOptions: [],
    });
    setVariantErrors({});
  };

  const validateVariantForm = () => {
    const errors: any = {};

    if (!variantFormData.sku.trim()) {
      errors.sku = 'SKU is required';
    }

    if (!variantFormData.name.trim()) {
      errors.name = 'Variant name is required';
    }

    if (!variantFormData.regularPrice || parseFloat(variantFormData.regularPrice) <= 0) {
      errors.regularPrice = 'Regular price must be greater than 0';
    }

    setVariantErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleVariantSubmit = async () => {
    if (!validateVariantForm()) {
      return;
    }

    try {
      const variantData = {
        sku: variantFormData.sku.trim(),
        name: variantFormData.name.trim(),
        description: variantFormData.description.trim(),
        regularPrice: parseFloat(variantFormData.regularPrice),
        salePrice: variantFormData.salePrice ? parseFloat(variantFormData.salePrice) : undefined,
        weight: variantFormData.weight ? parseFloat(variantFormData.weight) : undefined,
        hsn: variantFormData.hsn.trim() || undefined,
        isActive: variantFormData.isActive,
        seoTitle: variantFormData.seoTitle.trim() || undefined,
        seoDescription: variantFormData.seoDescription.trim() || undefined,
        seoSlug: variantFormData.seoSlug.trim() || undefined,
        options: variantFormData.variantOptions,
      };

      let response;
      if (editingVariant) {
        response = await api.updateProductVariant(productId, editingVariant.id, variantData);
      } else {
        response = await api.createProductVariant(productId, variantData);
      }

      if (response.success) {
        toast.success(`Variant ${editingVariant ? 'updated' : 'created'} successfully`);
        closeVariantForm();
        fetchProduct(); // Refresh product data
      } else {
        toast.error(response.error || `Failed to ${editingVariant ? 'update' : 'create'} variant`);
      }
    } catch (error) {
      toast.error(`Failed to ${editingVariant ? 'update' : 'create'} variant`);
    }
  };

  const deleteVariant = async (variantId: string) => {
    if (!confirm('Are you sure you want to delete this variant?')) {
      return;
    }

    try {
      const response = await api.deleteProductVariant(productId, variantId);
      if (response.success) {
        toast.success('Variant deleted successfully');
        fetchProduct(); // Refresh product data
      } else {
        toast.error('Failed to delete variant');
      }
    } catch (error) {
      toast.error('Failed to delete variant');
    }
  };

  if (loading) {
    return (
      <ProtectedRoute requiredRoles={['ADMIN', 'MANAGER']}>
        <DashboardLayout>
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  if (!product) {
    return (
      <ProtectedRoute requiredRoles={['ADMIN', 'MANAGER']}>
        <DashboardLayout>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Product not found</p>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRoles={['ADMIN', 'MANAGER']}>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/products')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
              </Button>
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
                <p className="text-muted-foreground">
                  Update product information and details.
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <Card>
            <CardContent className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-6">
                    <TabsTrigger value="basic">Basic Info</TabsTrigger>
                    <TabsTrigger value="variants">Variants</TabsTrigger>
                    <TabsTrigger value="images">Images</TabsTrigger>
                    <TabsTrigger value="categories">Categories</TabsTrigger>
                    <TabsTrigger value="related">Related Products</TabsTrigger>
                    <TabsTrigger value="reviews">Reviews</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Product Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter product name"
                        className={errors.name ? 'border-red-500' : ''}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-600">{errors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter product description"
                        className={errors.description ? 'border-red-500' : ''}
                        rows={4}
                      />
                      {errors.description && (
                        <p className="text-sm text-red-600">{errors.description}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={formData.status}
                        onValueChange={(value: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'ARCHIVED') =>
                          setFormData(prev => ({ ...prev, status: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="DRAFT">Draft</SelectItem>
                          <SelectItem value="ACTIVE">Active</SelectItem>
                          <SelectItem value="INACTIVE">Inactive</SelectItem>
                          <SelectItem value="ARCHIVED">Archived</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TabsContent>

                  <TabsContent value="images" className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">Product Images</h3>
                      <div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handleImageUpload(file);
                            }
                          }}
                          className="hidden"
                          ref={fileInputRef}
                          id="image-upload"
                        />
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={uploadingImage}
                          className="flex items-center gap-2"
                        >
                          <Upload className="h-4 w-4" />
                          Upload Image
                        </Button>
                      </div>
                    </div>

                    <div
                      className={`border-2 border-dashed rounded-lg p-8 flex flex-col items-center justify-center transition-colors ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-muted'}`}
                      onDragEnter={handleDrag}
                      onDragOver={handleDrag}
                      onDragLeave={handleDrag}
                      onDrop={handleDrop}
                      style={{ minHeight: '180px', cursor: 'pointer' }}
                      onClick={() => fileInputRef.current?.click()}
                    >
                      {formData.images.length === 0 ? (
                        <>
                          <Package className="h-12 w-12 mb-2 text-muted-foreground opacity-60" />
                          <div className="text-muted-foreground mb-1">No images uploaded</div>
                          <div className="text-xs text-muted-foreground">Click "Upload Image" or drag and drop to add product images</div>
                        </>
                      ) : (
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 w-full">
                          {formData.images.map((image, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={image.url}
                                alt={image.altText}
                                className="w-full h-32 object-cover rounded-lg border"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={e => { e.stopPropagation(); removeImage(index); }}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                      {uploadingImage && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white/60 z-10">
                          <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="categories" className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium mb-2">Categories</h3>
                        <div className="flex gap-2">
                          <Input
                            value={newCategory}
                            onChange={(e) => setNewCategory(e.target.value)}
                            placeholder="Add category"
                            onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCategory())}
                          />
                          <Button type="button" onClick={addCategory} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {formData.categories.map((category) => (
                            <Badge key={category} variant="secondary" className="flex items-center gap-1">
                              {category}
                              <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => removeCategory(category)}
                              />
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-medium mb-2">Tags</h3>
                        <div className="flex gap-2">
                          <Input
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            placeholder="Add tag"
                            onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                          />
                          <Button type="button" onClick={addTag} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {formData.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="flex items-center gap-1">
                              {tag}
                              <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => removeTag(tag)}
                              />
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="variants" className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Product Variants</h3>
                      <Button
                        type="button"
                        onClick={() => openVariantForm()}
                        size="sm"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Variant
                      </Button>
                    </div>

                    {variants.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No variants created yet</p>
                        <p className="text-sm">Add variants to manage different options like size, color, or material</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {variants.map((variant) => (
                          <Card key={variant.id}>
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-4">
                                    <div>
                                      <h4 className="font-medium">{variant.name}</h4>
                                      <p className="text-sm text-muted-foreground">SKU: {variant.sku}</p>
                                    </div>
                                    <div className="text-right">
                                      <p className="font-medium">${variant.regularPrice}</p>
                                      {variant.salePrice && (
                                        <p className="text-sm text-green-600">Sale: ${variant.salePrice}</p>
                                      )}
                                    </div>
                                  </div>

                                  {/* SEO Information for Variant */}
                                  {(variant.seoTitle || variant.seoDescription || variant.seoSlug) && (
                                    <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
                                      <h5 className="font-medium mb-2">SEO Information</h5>
                                      {variant.seoTitle && (
                                        <p><span className="font-medium">Title:</span> {variant.seoTitle}</p>
                                      )}
                                      {variant.seoDescription && (
                                        <p><span className="font-medium">Description:</span> {variant.seoDescription}</p>
                                      )}
                                      {variant.seoSlug && (
                                        <p><span className="font-medium">Slug:</span> {variant.seoSlug}</p>
                                      )}
                                    </div>
                                  )}
                                </div>

                                <div className="flex items-center gap-2">
                                  <Badge variant={variant.isActive ? "default" : "secondary"}>
                                    {variant.isActive ? "Active" : "Inactive"}
                                  </Badge>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => openVariantForm(variant)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => deleteVariant(variant.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="related" className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium mb-4">Related Products</h3>
                      <div className="space-y-6">

                        {/* Related Products */}
                        <div>
                          <h4 className="font-medium mb-2">Related Products</h4>
                          <p className="text-sm text-muted-foreground mb-3">Products that are similar or complementary</p>
                          <div className="border rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-3">
                              <div className="relative flex-1">
                                <Input
                                  placeholder="Search products to add..."
                                  value={productSearchQuery}
                                  onChange={(e) => {
                                    setProductSearchQuery(e.target.value);
                                    searchProducts(e.target.value);
                                  }}
                                />
                                {availableProducts.length > 0 && (
                                  <div className="absolute top-full left-0 right-0 bg-white border border-t-0 rounded-b-md shadow-lg z-10 max-h-40 overflow-y-auto">
                                    {availableProducts.map((product) => (
                                      <div
                                        key={product.id}
                                        className="p-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                                        onClick={() => addRelatedProduct(product.id, 'RELATED')}
                                      >
                                        <div className="font-medium">{product.name}</div>
                                        <div className="text-sm text-muted-foreground">{product.status}</div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>

                            {relatedProducts.length === 0 ? (
                              <div className="text-sm text-muted-foreground">No related products added</div>
                            ) : (
                              <div className="space-y-2">
                                {relatedProducts.map((relation) => (
                                  <div key={relation.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span className="font-medium">{relation.relatedProduct.name}</span>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => removeRelatedProduct(relation.id, 'Related')}
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Upsell Products */}
                        <div>
                          <h4 className="font-medium mb-2">Upsell Products</h4>
                          <p className="text-sm text-muted-foreground mb-3">Higher-value alternatives to suggest</p>
                          <div className="border rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-3">
                              <div className="relative flex-1">
                                <Input
                                  placeholder="Search products to add..."
                                  value={productSearchQuery}
                                  onChange={(e) => {
                                    setProductSearchQuery(e.target.value);
                                    searchProducts(e.target.value);
                                  }}
                                />
                                {availableProducts.length > 0 && (
                                  <div className="absolute top-full left-0 right-0 bg-white border border-t-0 rounded-b-md shadow-lg z-10 max-h-40 overflow-y-auto">
                                    {availableProducts.map((product) => (
                                      <div
                                        key={product.id}
                                        className="p-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                                        onClick={() => addRelatedProduct(product.id, 'UPSELL')}
                                      >
                                        <div className="font-medium">{product.name}</div>
                                        <div className="text-sm text-muted-foreground">{product.status}</div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>

                            {upsellProducts.length === 0 ? (
                              <div className="text-sm text-muted-foreground">No upsell products added</div>
                            ) : (
                              <div className="space-y-2">
                                {upsellProducts.map((relation) => (
                                  <div key={relation.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span className="font-medium">{relation.relatedProduct.name}</span>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => removeRelatedProduct(relation.id, 'Upsell')}
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Cross-sell Products */}
                        <div>
                          <h4 className="font-medium mb-2">Cross-sell Products</h4>
                          <p className="text-sm text-muted-foreground mb-3">Products often bought together</p>
                          <div className="border rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-3">
                              <div className="relative flex-1">
                                <Input
                                  placeholder="Search products to add..."
                                  value={productSearchQuery}
                                  onChange={(e) => {
                                    setProductSearchQuery(e.target.value);
                                    searchProducts(e.target.value);
                                  }}
                                />
                                {availableProducts.length > 0 && (
                                  <div className="absolute top-full left-0 right-0 bg-white border border-t-0 rounded-b-md shadow-lg z-10 max-h-40 overflow-y-auto">
                                    {availableProducts.map((product) => (
                                      <div
                                        key={product.id}
                                        className="p-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                                        onClick={() => addRelatedProduct(product.id, 'CROSS_SELL')}
                                      >
                                        <div className="font-medium">{product.name}</div>
                                        <div className="text-sm text-muted-foreground">{product.status}</div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>

                            {crossSellProducts.length === 0 ? (
                              <div className="text-sm text-muted-foreground">No cross-sell products added</div>
                            ) : (
                              <div className="space-y-2">
                                {crossSellProducts.map((relation) => (
                                  <div key={relation.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span className="font-medium">{relation.relatedProduct.name}</span>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => removeRelatedProduct(relation.id, 'Cross-sell')}
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="reviews" className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium mb-4">Product Reviews</h3>

                      {reviews.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No reviews yet</p>
                          <p className="text-sm">Customer reviews will appear here once submitted</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {reviews.map((review) => (
                            <Card key={review.id}>
                              <CardContent className="p-4">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                      <div className="flex">
                                        {[1, 2, 3, 4, 5].map((star) => (
                                          <Star
                                            key={star}
                                            className={`h-4 w-4 ${
                                              star <= review.rating
                                                ? 'text-yellow-400 fill-current'
                                                : 'text-gray-300'
                                            }`}
                                          />
                                        ))}
                                      </div>
                                      <span className="text-sm text-muted-foreground">
                                        by {review.customer?.firstName} {review.customer?.lastName}
                                      </span>
                                      <Badge variant={review.isApproved ? "default" : "secondary"}>
                                        {review.isApproved ? "Approved" : "Pending"}
                                      </Badge>
                                    </div>
                                    {review.title && (
                                      <h4 className="font-medium mb-1">{review.title}</h4>
                                    )}
                                    {review.comment && (
                                      <p className="text-sm text-muted-foreground">{review.comment}</p>
                                    )}
                                    <p className="text-xs text-muted-foreground mt-2">
                                      {new Date(review.createdAt).toLocaleDateString()}
                                    </p>
                                  </div>

                                  <div className="flex items-center gap-2">
                                    {!review.isApproved && (
                                      <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => approveReview(review.id)}
                                      >
                                        <Check className="h-4 w-4 mr-1" />
                                        Approve
                                      </Button>
                                    )}
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => deleteReview(review.id)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>

                {/* Form Actions */}
                <div className="flex items-center justify-end gap-4 pt-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push('/products')}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      'Update Product'
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Variant Form Dialog */}
          {showVariantForm && (
            <div className="fixed inset-0  bg-grey bg-opacity-60 flex items-center justify-center z-50">
              <div className="bg-black rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">
                    {editingVariant ? 'Edit Variant' : 'Add New Variant'}
                  </h3>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={closeVariantForm}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-4">
                  {/* Basic Information */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>SKU *</Label>
                      <Input
                        value={variantFormData.sku}
                        onChange={(e) => setVariantFormData(prev => ({ ...prev, sku: e.target.value }))}
                        placeholder="Enter SKU"
                        className={variantErrors.sku ? 'border-red-500' : ''}
                      />
                      {variantErrors.sku && (
                        <p className="text-sm text-red-600">{variantErrors.sku}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label>Variant Name *</Label>
                      <Input
                        value={variantFormData.name}
                        onChange={(e) => setVariantFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter variant name"
                        className={variantErrors.name ? 'border-red-500' : ''}
                      />
                      {variantErrors.name && (
                        <p className="text-sm text-red-600">{variantErrors.name}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      value={variantFormData.description}
                      onChange={(e) => setVariantFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter variant description"
                      rows={2}
                    />
                  </div>

                  {/* Pricing */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Regular Price *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={variantFormData.regularPrice}
                        onChange={(e) => setVariantFormData(prev => ({ ...prev, regularPrice: e.target.value }))}
                        placeholder="0.00"
                        className={variantErrors.regularPrice ? 'border-red-500' : ''}
                      />
                      {variantErrors.regularPrice && (
                        <p className="text-sm text-red-600">{variantErrors.regularPrice}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label>Sale Price</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={variantFormData.salePrice}
                        onChange={(e) => setVariantFormData(prev => ({ ...prev, salePrice: e.target.value }))}
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  {/* Weight and HSN */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Weight (g)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={variantFormData.weight}
                        onChange={(e) => setVariantFormData(prev => ({ ...prev, weight: e.target.value }))}
                        placeholder="0.00"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>HSN Code</Label>
                      <Input
                        value={variantFormData.hsn}
                        onChange={(e) => setVariantFormData(prev => ({ ...prev, hsn: e.target.value }))}
                        placeholder="Enter HSN code"
                      />
                    </div>
                  </div>

                  {/* SEO Fields */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-3">SEO Information</h4>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>SEO Title</Label>
                        <Input
                          value={variantFormData.seoTitle}
                          onChange={(e) => setVariantFormData(prev => ({ ...prev, seoTitle: e.target.value }))}
                          placeholder="SEO title for this variant"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>SEO Description</Label>
                        <Textarea
                          value={variantFormData.seoDescription}
                          onChange={(e) => setVariantFormData(prev => ({ ...prev, seoDescription: e.target.value }))}
                          placeholder="SEO description for this variant"
                          rows={2}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>SEO Slug</Label>
                        <Input
                          value={variantFormData.seoSlug}
                          onChange={(e) => setVariantFormData(prev => ({ ...prev, seoSlug: e.target.value }))}
                          placeholder="SEO-friendly URL slug (e.g. variant-name)"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Active Status */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="variant-active"
                      checked={variantFormData.isActive}
                      onChange={(e) => setVariantFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="variant-active">Active</Label>
                  </div>

                  {/* Form Actions */}
                  <div className="flex items-center justify-end gap-4 pt-4 border-t">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={closeVariantForm}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="button"
                      onClick={handleVariantSubmit}
                    >
                      {editingVariant ? 'Update Variant' : 'Create Variant'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
