{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/theme-provider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\n\r\ninterface ThemeProviderProps {\r\n    children: React.ReactNode\r\n    attribute?: \"class\" | \"data-theme\" | \"data-color-scheme\"\r\n    defaultTheme?: string\r\n    enableSystem?: boolean\r\n    disableTransitionOnChange?: boolean\r\n}\r\n\r\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\r\n    return <NextThemesProvider {...props}>{children}</NextThemesProvider>\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAaO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACpE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AAC3C", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/lib/api.ts"], "sourcesContent": ["import { toast } from \"sonner\";\r\n\r\nconst API_BASE_URL =\r\n  process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5000/api\";\r\n\r\n// Types for API responses\r\nexport interface ApiResponse<T = unknown> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n}\r\n\r\nexport interface PaginatedData<T> {\r\n  items?: T[];\r\n  users?: T[];\r\n  customers?: T[];\r\n  products?: T[];\r\n  orders?: T[];\r\n  collections?: T[];\r\n  data?: T[];\r\n  pagination: {\r\n    page: number;\r\n    limit: number;\r\n    total: number;\r\n    pages: number;\r\n  };\r\n}\r\n\r\nexport interface Promotion {\r\n  id: string;\r\n  code: string;\r\n  name: string;\r\n  description?: string;\r\n  type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SHIPPING' | 'BOGO' | 'VOLUME_DISCOUNT';\r\n  value: number;\r\n  minOrderAmount?: number;\r\n  maxDiscount?: number;\r\n  usageLimit?: number;\r\n  usageCount: number;\r\n  isActive: boolean;\r\n  startsAt?: string;\r\n  expiresAt?: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface TaxRate {\r\n  id: string;\r\n  country: string;\r\n  state?: string;\r\n  rate: number;\r\n  type: string;\r\n  isActive: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  success: boolean;\r\n  data?: {\r\n    items: T[];\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    totalPages: number;\r\n  };\r\n  error?: string;\r\n}\r\n\r\n// User types\r\nexport interface User {\r\n  id: string;\r\n  email: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  role: \"ADMIN\" | \"MANAGER\" | \"STAFF\";\r\n  isActive: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  permissions?: Permission[];\r\n}\r\n\r\nexport interface Permission {\r\n  id: string;\r\n  module: string;\r\n  action: string;\r\n  granted: boolean;\r\n}\r\n\r\n// Customer types\r\nexport interface Customer {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  middleName?: string;\r\n  email: string;\r\n  mobile: string;\r\n  customerType: \"B2C\" | \"B2B\" | \"ENTERPRISE\";\r\n  isActive: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  addresses?: Address[];\r\n  customerTags?: CustomerTag[];\r\n  _count?: {\r\n    orders: number;\r\n    addresses: number;\r\n    reviews: number;\r\n  };\r\n}\r\n\r\nexport interface Address {\r\n  id: string;\r\n  customerId: string;\r\n  type: \"BILLING\" | \"SHIPPING\";\r\n  firstName: string;\r\n  lastName: string;\r\n  company?: string;\r\n  address1: string;\r\n  address2?: string;\r\n  city: string;\r\n  state: string;\r\n  postalCode: string;\r\n  country: string;\r\n  phone?: string;\r\n  isDefault: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface CustomerTag {\r\n  id: string;\r\n  tag: string;\r\n}\r\n\r\n// Product types\r\nexport interface Product {\r\n  id: string;\r\n  name: string;\r\n  description?: string;\r\n  status: \"DRAFT\" | \"ACTIVE\" | \"INACTIVE\" | \"ARCHIVED\";\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  seoTitle?: string;\r\n  seoDescription?: string;\r\n  seoSlug?: string;\r\n  variants?: ProductVariant[];\r\n  images?: ProductImage[];\r\n  categories?: ProductCategory[];\r\n  tags?: ProductTag[];\r\n  _count?: {\r\n    variants: number;\r\n    reviews: number;\r\n  };\r\n}\r\n\r\nexport interface ProductVariant {\r\n  id: string;\r\n  productId: string;\r\n  name: string;\r\n  description?: string;\r\n  sku: string;\r\n  barcode?: string;\r\n  regularPrice: number;\r\n  salePrice?: number;\r\n  costPrice?: number;\r\n  weight?: number;\r\n  hsn?: string;\r\n  isActive: boolean;\r\n  dimensions?: {\r\n    length: number;\r\n    width: number;\r\n    height: number;\r\n  };\r\n  inventory?: {\r\n    id: string;\r\n    locationId: string;\r\n    quantity: number;\r\n    reservedQty: number;\r\n  }[];\r\n  variantOptions?: VariantOption[];\r\n  segmentPrices?: {\r\n    id: string;\r\n    customerType: \"B2C\" | \"B2B\" | \"WHOLESALE\";\r\n    regularPrice: number;\r\n    salePrice?: number;\r\n  }[];\r\n  seoTitle?: string;\r\n  seoDescription?: string;\r\n  seoSlug?: string;\r\n}\r\n\r\nexport interface VariantOption {\r\n  id: string;\r\n  name: string;\r\n  value: string;\r\n}\r\n\r\nexport interface ProductImage {\r\n  id: string;\r\n  url: string;\r\n  altText?: string;\r\n  sortOrder: number;\r\n}\r\n\r\nexport interface ProductCategory {\r\n  id: string;\r\n  name: string;\r\n}\r\n\r\nexport interface ProductTag {\r\n  id: string;\r\n  tag: string;\r\n}\r\n\r\nexport interface Inventory {\r\n  id: string;\r\n  quantity: number;\r\n  reservedQty: number;\r\n  locationId: string;\r\n  location?: {\r\n    id: string;\r\n    name: string;\r\n  };\r\n}\r\n\r\n// Order types\r\nexport interface Order {\r\n  id: string;\r\n  orderNumber: string;\r\n  customerId: string;\r\n  userId?: string;\r\n  status:\r\n    | \"PENDING\"\r\n    | \"PROCESSING\"\r\n    | \"SHIPPED\"\r\n    | \"DELIVERED\"\r\n    | \"CANCELLED\"\r\n    | \"REFUNDED\"\r\n    | \"ON_HOLD\";\r\n  subtotal: number;\r\n  discountAmount: number;\r\n  shippingAmount: number;\r\n  taxAmount: number;\r\n  totalAmount: number;\r\n  billingAddressId: string;\r\n  shippingAddressId: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  customer?: Customer;\r\n  billingAddress?: Address;\r\n  shippingAddress?: Address;\r\n  items?: OrderItem[];\r\n  payments?: Payment[];\r\n  shipments?: Shipment[];\r\n  notes?: OrderNote[];\r\n  auditLogs?: AuditLog[];\r\n  _count?: {\r\n    items: number;\r\n    notes: number;\r\n  };\r\n}\r\n\r\nexport interface OrderItem {\r\n  id: string;\r\n  orderId: string;\r\n  variantId: string;\r\n  quantity: number;\r\n  unitPrice: number;\r\n  totalPrice: number;\r\n  variant?: ProductVariant & {\r\n    product?: Product;\r\n  };\r\n}\r\n\r\nexport interface OrderNote {\r\n  id: string;\r\n  orderId: string;\r\n  userId: string;\r\n  note: string;\r\n  isInternal: boolean;\r\n  createdAt: string;\r\n  user?: User;\r\n}\r\n\r\nexport interface Payment {\r\n  id: string;\r\n  orderId: string;\r\n  paymentMethod: string;\r\n  status: string;\r\n  amount: number;\r\n  paidAt?: string;\r\n}\r\n\r\nexport interface Shipment {\r\n  id: string;\r\n  orderId: string;\r\n  carrier: string;\r\n  trackingNumber?: string;\r\n  status: string;\r\n  shippedAt?: string;\r\n}\r\n\r\nexport interface AuditLog {\r\n  id: string;\r\n  orderId?: string;\r\n  userId: string;\r\n  action: string;\r\n  details: any;\r\n  ipAddress?: string;\r\n  userAgent?: string;\r\n  createdAt: string;\r\n  user?: User;\r\n}\r\n\r\n// Token management\r\nexport const TOKEN_KEY = \"auth_token\";\r\n\r\nexport const getToken = (): string | null => {\r\n  if (typeof window === \"undefined\") return null;\r\n  return localStorage.getItem(TOKEN_KEY);\r\n};\r\n\r\nexport const setToken = (token: string): void => {\r\n  if (typeof window === \"undefined\") return;\r\n  localStorage.setItem(TOKEN_KEY, token);\r\n};\r\n\r\nexport const removeToken = (): void => {\r\n  if (typeof window === \"undefined\") return;\r\n  localStorage.removeItem(TOKEN_KEY);\r\n};\r\n\r\n// Collection types\r\nexport interface Collection {\r\n  id: string;\r\n  name: string;\r\n  description?: string;\r\n  slug: string;\r\n  isActive: boolean;\r\n  sortOrder: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  products?: ProductCollection[];\r\n  _count?: {\r\n    products: number;\r\n  };\r\n}\r\n\r\nexport interface ProductCollection {\r\n  id: string;\r\n  collectionId: string;\r\n  productId: string;\r\n  sortOrder: number;\r\n  product?: Product;\r\n}\r\n\r\n// API client class\r\nclass ApiClient {\r\n  private baseURL: string;\r\n\r\n  constructor(baseURL: string) {\r\n    this.baseURL = baseURL;\r\n  }\r\n\r\n  private getHeaders(): HeadersInit {\r\n    const headers: HeadersInit = {\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n\r\n    const token = getToken();\r\n    if (token) {\r\n      headers.Authorization = `Bearer ${token}`;\r\n      console.log('[API] Sending Authorization header:', headers.Authorization);\r\n    } else {\r\n      console.log('[API] No token found for Authorization header');\r\n    }\r\n\r\n    return headers;\r\n  }\r\n\r\n  private async request<T>(\r\n    endpoint: string,\r\n    options: RequestInit = {}\r\n  ): Promise<ApiResponse<T>> {\r\n    try {\r\n      const url = `${this.baseURL}${endpoint}`;\r\n      const config: RequestInit = {\r\n        headers: this.getHeaders(),\r\n        ...options,\r\n      };\r\n\r\n      const response = await fetch(url, config);\r\n      const data = await response.json();\r\n\r\n      // Check for 401 Unauthorized first\r\n      if (response.status === 401) {\r\n        removeToken();\r\n        // Don't redirect here - let the ProtectedRoute handle it\r\n        return {\r\n          success: false,\r\n          error: \"Authentication required\",\r\n        };\r\n      }\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          data.error || `HTTP ${response.status}: ${response.statusText}`\r\n        );\r\n      }\r\n\r\n      return data;\r\n    } catch (error) {\r\n      console.error(\"API request failed:\", error);\r\n\r\n      if (error instanceof Error) {\r\n        // Show error toast\r\n        toast.error(error.message);\r\n\r\n        return {\r\n          success: false,\r\n          error: error.message,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: \"An unexpected error occurred\",\r\n      };\r\n    }\r\n  }\r\n\r\n  // Add generic HTTP methods\r\n  async get<T = any>(endpoint: string): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, { method: \"GET\" });\r\n  }\r\n  async post<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, {\r\n      method: \"POST\",\r\n      headers: { ...this.getHeaders(), \"Content-Type\": \"application/json\" },\r\n      body: body ? JSON.stringify(body) : undefined,\r\n    });\r\n  }\r\n  async put<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, {\r\n      method: \"PUT\",\r\n      headers: { ...this.getHeaders(), \"Content-Type\": \"application/json\" },\r\n      body: body ? JSON.stringify(body) : undefined,\r\n    });\r\n  }\r\n  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, { method: \"DELETE\", headers: this.getHeaders() });\r\n  }\r\n\r\n  // Authentication endpoints\r\n  async login(\r\n    email: string,\r\n    password: string\r\n  ): Promise<ApiResponse<{ user: User; token: string }>> {\r\n    return this.request(\"/auth/login\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify({ email, password }),\r\n    });\r\n  }\r\n\r\n  async register(userData: {\r\n    email: string;\r\n    password: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    role?: string;\r\n  }): Promise<ApiResponse<{ user: User; token: string }>> {\r\n    return this.request(\"/auth/register\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(userData),\r\n    });\r\n  }\r\n\r\n  async getProfile(): Promise<ApiResponse<User>> {\r\n    return this.request(\"/auth/profile\");\r\n  }\r\n\r\n  async updateProfile(userData: {\r\n    firstName?: string;\r\n    lastName?: string;\r\n    email?: string;\r\n  }): Promise<ApiResponse<User>> {\r\n    return this.request(\"/auth/profile\", {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(userData),\r\n    });\r\n  }\r\n\r\n  async changePassword(\r\n    currentPassword: string,\r\n    newPassword: string\r\n  ): Promise<ApiResponse> {\r\n    return this.request(\"/auth/change-password\", {\r\n      method: \"PUT\",\r\n      body: JSON.stringify({ currentPassword, newPassword }),\r\n    });\r\n  }\r\n\r\n  async logout(): Promise<ApiResponse> {\r\n    return this.request(\"/auth/logout\", {\r\n      method: \"POST\",\r\n    });\r\n  }\r\n\r\n  // User management endpoints\r\n  async getUsers(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    role?: string;\r\n    isActive?: boolean;\r\n    search?: string;\r\n  }): Promise<PaginatedResponse<User>> {\r\n    const searchParams = new URLSearchParams();\r\n    if (params) {\r\n      Object.entries(params).forEach(([key, value]) => {\r\n        if (value !== undefined) {\r\n          searchParams.append(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n    return this.request(`/users?${searchParams.toString()}`) as Promise<\r\n      PaginatedResponse<User>\r\n    >;\r\n  }\r\n\r\n  async getUser(id: string): Promise<ApiResponse<User>> {\r\n    return this.request(`/users/${id}`);\r\n  }\r\n\r\n  async createUser(userData: {\r\n    email: string;\r\n    password: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    role: string;\r\n    isActive?: boolean;\r\n  }): Promise<ApiResponse<User>> {\r\n    return this.request(\"/users\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(userData),\r\n    });\r\n  }\r\n\r\n  async updateUser(\r\n    id: string,\r\n    userData: {\r\n      email?: string;\r\n      firstName?: string;\r\n      lastName?: string;\r\n      role?: string;\r\n      isActive?: boolean;\r\n    }\r\n  ): Promise<ApiResponse<User>> {\r\n    return this.request(`/users/${id}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(userData),\r\n    });\r\n  }\r\n\r\n  async deleteUser(id: string): Promise<ApiResponse> {\r\n    return this.request(`/users/${id}`, {\r\n      method: \"DELETE\",\r\n    });\r\n  }\r\n\r\n  async resetUserPassword(\r\n    id: string,\r\n    newPassword: string\r\n  ): Promise<ApiResponse> {\r\n    return this.request(`/users/${id}/reset-password`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify({ newPassword }),\r\n    });\r\n  }\r\n\r\n  async updateUserPermissions(\r\n    id: string,\r\n    permissions: Permission[]\r\n  ): Promise<ApiResponse<User>> {\r\n    return this.request(`/users/${id}/permissions`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify({ permissions }),\r\n    });\r\n  }\r\n\r\n  // Customer management endpoints\r\n  async getCustomers(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    customerType?: string;\r\n    isActive?: boolean;\r\n    search?: string;\r\n  }): Promise<\r\n    ApiResponse<{\r\n      customers: Customer[];\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>\r\n  > {\r\n    const searchParams = new URLSearchParams();\r\n    if (params) {\r\n      Object.entries(params).forEach(([key, value]) => {\r\n        if (value !== undefined) {\r\n          searchParams.append(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await this.request<{\r\n      customers: Customer[];\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>(`/customers?${searchParams.toString()}`);\r\n\r\n    if (response.success && response.data) {\r\n      return {\r\n        success: true,\r\n        data: {\r\n          customers: response.data.customers,\r\n          pagination: response.data.pagination,\r\n        },\r\n      };\r\n    }\r\n\r\n    return response as ApiResponse<{\r\n      customers: Customer[];\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>;\r\n  }\r\n\r\n  async getCustomer(id: string): Promise<ApiResponse<Customer>> {\r\n    return this.request(`/customers/${id}`);\r\n  }\r\n\r\n  async createCustomer(customerData: {\r\n    firstName: string;\r\n    lastName: string;\r\n    middleName?: string;\r\n    email: string;\r\n    mobile: string;\r\n    customerType?: string;\r\n    isActive?: boolean;\r\n    tags?: string[];\r\n    addresses?: any[];\r\n  }): Promise<ApiResponse<Customer>> {\r\n    return this.request(\"/customers\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(customerData),\r\n    });\r\n  }\r\n\r\n  async updateCustomer(\r\n    id: string,\r\n    customerData: {\r\n      firstName?: string;\r\n      lastName?: string;\r\n      middleName?: string;\r\n      email?: string;\r\n      mobile?: string;\r\n      customerType?: string;\r\n      isActive?: boolean;\r\n      tags?: string[];\r\n    }\r\n  ): Promise<ApiResponse<Customer>> {\r\n    return this.request(`/customers/${id}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(customerData),\r\n    });\r\n  }\r\n\r\n  async deleteCustomer(id: string): Promise<ApiResponse> {\r\n    return this.request(`/customers/${id}`, {\r\n      method: \"DELETE\",\r\n    });\r\n  }\r\n\r\n  // Product management endpoints\r\n  async getProducts(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    status?: string;\r\n    search?: string;\r\n    category?: string;\r\n    include?: {\r\n      variants?: {\r\n        include?: {\r\n          inventory?: boolean;\r\n        };\r\n      };\r\n    };\r\n  }): Promise<ApiResponse<PaginatedData<Product>>> {\r\n    const queryParams = new URLSearchParams();\r\n    if (params?.page) queryParams.append(\"page\", params.page.toString());\r\n    if (params?.limit) queryParams.append(\"limit\", params.limit.toString());\r\n    if (params?.status) queryParams.append(\"status\", params.status);\r\n    if (params?.search) queryParams.append(\"search\", params.search);\r\n    if (params?.category) queryParams.append(\"category\", params.category);\r\n    if (params?.include?.variants?.include?.inventory) {\r\n      queryParams.append(\"include\", \"variants.inventory\");\r\n    }\r\n\r\n    const response = await this.request<{\r\n      products: Product[];\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>(`/products?${queryParams}`);\r\n\r\n    if (response.success && response.data) {\r\n      return {\r\n        success: true,\r\n        data: {\r\n          products: response.data.products,\r\n          pagination: response.data.pagination,\r\n        },\r\n      };\r\n    }\r\n\r\n    return response as ApiResponse<PaginatedData<Product>>;\r\n  }\r\n\r\n  async getProduct(id: string): Promise<ApiResponse<Product>> {\r\n    return this.request(`/products/${id}`);\r\n  }\r\n\r\n  async createProduct(productData: {\r\n    name: string;\r\n    description?: string;\r\n    status?: string;\r\n    categories?: string[];\r\n    tags?: string[];\r\n    images?: any[];\r\n    variants: any[];\r\n    seoTitle?: string;\r\n    seoDescription?: string;\r\n    seoSlug?: string;\r\n  }): Promise<ApiResponse<Product>> {\r\n    return this.request(\"/products\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(productData),\r\n    });\r\n  }\r\n\r\n  async updateProduct(\r\n    id: string,\r\n    productData: {\r\n      name?: string;\r\n      description?: string;\r\n      status?: string;\r\n      categories?: string[];\r\n      tags?: string[];\r\n      images?: any[];\r\n      seoTitle?: string;\r\n      seoDescription?: string;\r\n      seoSlug?: string;\r\n    }\r\n  ): Promise<ApiResponse<Product>> {\r\n    return this.request(`/products/${id}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(productData),\r\n    });\r\n  }\r\n\r\n  async deleteProduct(id: string): Promise<ApiResponse> {\r\n    return this.request(`/products/${id}`, {\r\n      method: \"DELETE\",\r\n    });\r\n  }\r\n\r\n  // Order management endpoints\r\n  async getOrders(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    status?: string;\r\n    customerId?: string;\r\n    search?: string;\r\n    dateFrom?: string;\r\n    dateTo?: string;\r\n  }): Promise<ApiResponse<PaginatedData<Order>>> {\r\n    const searchParams = new URLSearchParams();\r\n    if (params) {\r\n      Object.entries(params).forEach(([key, value]) => {\r\n        if (value !== undefined) {\r\n          searchParams.append(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await this.request<{\r\n      orders: Order[];\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>(`/orders?${searchParams.toString()}`);\r\n\r\n    if (response.success && response.data) {\r\n      return {\r\n        success: true,\r\n        data: {\r\n          orders: response.data.orders,\r\n          pagination: response.data.pagination,\r\n        },\r\n      };\r\n    }\r\n\r\n    return response as ApiResponse<PaginatedData<Order>>;\r\n  }\r\n\r\n  async getOrder(id: string): Promise<ApiResponse<Order>> {\r\n    return this.request(`/orders/${id}`);\r\n  }\r\n\r\n  async createOrder(orderData: {\r\n    customerId: string;\r\n    billingAddressId: string;\r\n    shippingAddressId: string;\r\n    items: {\r\n      variantId: string;\r\n      quantity: number;\r\n      unitPrice: string;\r\n    }[];\r\n    discountAmount?: string;\r\n    shippingAmount?: string;\r\n    taxAmount?: string;\r\n    couponCode?: string;\r\n  }): Promise<ApiResponse<Order>> {\r\n    return this.request(\"/orders\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(orderData),\r\n    });\r\n  }\r\n\r\n  async updateOrder(\r\n    id: string,\r\n    orderData: {\r\n      status?: string;\r\n      billingAddressId?: string;\r\n      shippingAddressId?: string;\r\n      discountAmount?: string;\r\n      shippingAmount?: string;\r\n      taxAmount?: string;\r\n    }\r\n  ): Promise<ApiResponse<Order>> {\r\n    return this.request(`/orders/${id}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(orderData),\r\n    });\r\n  }\r\n\r\n  async updateOrderStatus(\r\n    id: string,\r\n    status: string,\r\n    note?: string\r\n  ): Promise<ApiResponse> {\r\n    return this.request(`/orders/${id}/status`, {\r\n      method: \"PATCH\",\r\n      body: JSON.stringify({ status, note }),\r\n    });\r\n  }\r\n\r\n  async addOrderNote(\r\n    id: string,\r\n    note: string,\r\n    isInternal: boolean = true\r\n  ): Promise<ApiResponse<OrderNote>> {\r\n    return this.request(`/orders/${id}/notes`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify({ note, isInternal }),\r\n    });\r\n  }\r\n\r\n  async getOrderNotes(id: string): Promise<ApiResponse<OrderNote[]>> {\r\n    return this.request(`/orders/${id}/notes`);\r\n  }\r\n\r\n  async deleteOrder(id: string): Promise<ApiResponse> {\r\n    return this.request(`/orders/${id}`, {\r\n      method: \"DELETE\",\r\n    });\r\n  }\r\n\r\n  // File upload\r\n  async uploadFile(\r\n    file: File\r\n  ): Promise<ApiResponse<{ url: string; filename: string }>> {\r\n    const formData = new FormData();\r\n    formData.append(\"file\", file);\r\n\r\n    const token = getToken();\r\n    const headers: HeadersInit = {};\r\n    if (token) {\r\n      headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(`${this.baseURL}/upload`, {\r\n        method: \"POST\",\r\n        headers,\r\n        body: formData,\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          data.error || `HTTP ${response.status}: ${response.statusText}`\r\n        );\r\n      }\r\n\r\n      return data;\r\n    } catch (error) {\r\n      console.error(\"File upload failed:\", error);\r\n      if (error instanceof Error) {\r\n        toast.error(error.message);\r\n        return {\r\n          success: false,\r\n          error: error.message,\r\n        };\r\n      }\r\n      return {\r\n        success: false,\r\n        error: \"File upload failed\",\r\n      };\r\n    }\r\n  }\r\n\r\n  async createAddress(\r\n    customerId: string,\r\n    addressData: any\r\n  ): Promise<ApiResponse<Address>> {\r\n    return this.request(`/customers/${customerId}/addresses`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(addressData),\r\n    });\r\n  }\r\n\r\n  async updateAddress(\r\n    customerId: string,\r\n    addressId: string,\r\n    addressData: any\r\n  ): Promise<ApiResponse<Address>> {\r\n    return this.request(`/customers/${customerId}/addresses/${addressId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(addressData),\r\n    });\r\n  }\r\n\r\n  async deleteAddress(\r\n    customerId: string,\r\n    addressId: string\r\n  ): Promise<ApiResponse> {\r\n    return this.request(`/customers/${customerId}/addresses/${addressId}`, {\r\n      method: \"DELETE\",\r\n    });\r\n  }\r\n\r\n  // Inventory Management\r\n  async getLocations(): Promise<ApiResponse<Location[]>> {\r\n    return this.request(\"/locations\");\r\n  }\r\n\r\n  async getInventory(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    locationId?: string;\r\n    lowStock?: boolean;\r\n  }): Promise<\r\n    ApiResponse<{\r\n      inventory: Array<{\r\n        id: string;\r\n        quantity: number;\r\n        lowStockAlert: number;\r\n        variant: {\r\n          id: string;\r\n          sku: string;\r\n          name: string;\r\n          product: {\r\n            name: string;\r\n            status: string;\r\n          };\r\n        };\r\n        location: {\r\n          id: string;\r\n          name: string;\r\n        };\r\n      }>;\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>\r\n  > {\r\n    const searchParams = new URLSearchParams();\r\n    if (params) {\r\n      if (params.page) searchParams.append(\"page\", params.page.toString());\r\n      if (params.limit) searchParams.append(\"limit\", params.limit.toString());\r\n      if (params.search) searchParams.append(\"search\", params.search);\r\n      if (params.locationId)\r\n        searchParams.append(\"locationId\", params.locationId);\r\n      if (params.lowStock)\r\n        searchParams.append(\"lowStock\", params.lowStock.toString());\r\n    }\r\n    const queryString = searchParams.toString();\r\n    return this.request(`/inventory${queryString ? `?${queryString}` : \"\"}`);\r\n  }\r\n\r\n  async updateInventory(\r\n    id: string,\r\n    data: {\r\n      quantity?: number;\r\n      lowStockAlert?: number;\r\n      reason: string;\r\n    }\r\n  ): Promise<ApiResponse<any>> {\r\n    return this.request(`/inventory/${id}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(data),\r\n    });\r\n  }\r\n\r\n  async createInventoryMovement(data: {\r\n    variantId: string;\r\n    locationId: string;\r\n    quantity: number;\r\n    type:\r\n      | \"PURCHASE\"\r\n      | \"SALE\"\r\n      | \"RETURN\"\r\n      | \"ADJUSTMENT_IN\"\r\n      | \"ADJUSTMENT_OUT\"\r\n      | \"TRANSFER_IN\"\r\n      | \"TRANSFER_OUT\";\r\n    reason: string;\r\n  }): Promise<ApiResponse<any>> {\r\n    return this.request(\"/inventory/movement\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(data),\r\n    });\r\n  }\r\n\r\n  // Category Management\r\n  async getCategories(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n  }): Promise<\r\n    ApiResponse<{\r\n      categories: Array<{\r\n        id: string;\r\n        name: string;\r\n        product: {\r\n          name: string;\r\n          status: string;\r\n        };\r\n      }>;\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>\r\n  > {\r\n    const searchParams = new URLSearchParams();\r\n    if (params) {\r\n      if (params.page) searchParams.append(\"page\", params.page.toString());\r\n      if (params.limit) searchParams.append(\"limit\", params.limit.toString());\r\n      if (params.search) searchParams.append(\"search\", params.search);\r\n    }\r\n    const queryString = searchParams.toString();\r\n    return this.request(`/categories${queryString ? `?${queryString}` : \"\"}`);\r\n  }\r\n\r\n  async createCategory(data: { name: string; productId: string }): Promise<\r\n    ApiResponse<{\r\n      id: string;\r\n      name: string;\r\n      product: {\r\n        name: string;\r\n        status: string;\r\n      };\r\n    }>\r\n  > {\r\n    return this.request(\"/categories\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(data),\r\n    });\r\n  }\r\n\r\n  async updateCategory(\r\n    id: string,\r\n    data: {\r\n      name: string;\r\n    }\r\n  ): Promise<\r\n    ApiResponse<{\r\n      id: string;\r\n      name: string;\r\n      product: {\r\n        name: string;\r\n        status: string;\r\n      };\r\n    }>\r\n  > {\r\n    return this.request(`/categories/${id}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(data),\r\n    });\r\n  }\r\n\r\n  async deleteCategory(id: string): Promise<ApiResponse<void>> {\r\n    return this.request(`/categories/${id}`, {\r\n      method: \"DELETE\",\r\n    });\r\n  }\r\n\r\n  async getCollections(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    isActive?: boolean;\r\n  }): Promise<\r\n    ApiResponse<{\r\n      collections: Collection[];\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>\r\n  > {\r\n    const queryParams = new URLSearchParams();\r\n    if (params?.page) queryParams.append(\"page\", params.page.toString());\r\n    if (params?.limit) queryParams.append(\"limit\", params.limit.toString());\r\n    if (params?.search) queryParams.append(\"search\", params.search);\r\n    if (typeof params?.isActive === \"boolean\")\r\n      queryParams.append(\"isActive\", params.isActive.toString());\r\n\r\n    return this.request<{\r\n      collections: Collection[];\r\n      pagination: {\r\n        page: number;\r\n        limit: number;\r\n        total: number;\r\n        pages: number;\r\n      };\r\n    }>(`/collections?${queryParams.toString()}`);\r\n  }\r\n\r\n  async getCollection(id: string): Promise<ApiResponse<Collection>> {\r\n    return this.request<Collection>(`/collections/${id}`);\r\n  }\r\n\r\n  async createCollection(collectionData: {\r\n    name: string;\r\n    description?: string;\r\n    isActive?: boolean;\r\n    sortOrder?: number;\r\n    productIds?: string[];\r\n  }): Promise<ApiResponse<Collection>> {\r\n    return this.request<Collection>(\"/collections\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(collectionData),\r\n    });\r\n  }\r\n\r\n  async updateCollection(\r\n    id: string,\r\n    collectionData: {\r\n      name?: string;\r\n      description?: string;\r\n      isActive?: boolean;\r\n      sortOrder?: number;\r\n    }\r\n  ): Promise<ApiResponse<Collection>> {\r\n    return this.request<Collection>(`/collections/${id}`, {\r\n      method: \"PATCH\",\r\n      body: JSON.stringify(collectionData),\r\n    });\r\n  }\r\n\r\n  async deleteCollection(id: string): Promise<ApiResponse> {\r\n    return this.request(`/collections/${id}`, {\r\n      method: \"DELETE\",\r\n    });\r\n  }\r\n\r\n  async updateCollectionProducts(\r\n    id: string,\r\n    productIds: string[]\r\n  ): Promise<ApiResponse> {\r\n    return this.request(`/collections/${id}/products`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify({ productIds }),\r\n    });\r\n  }\r\n\r\n  async reorderCollectionProducts(\r\n    id: string,\r\n    productOrders: Array<{ productId: string; sortOrder: number }>\r\n  ): Promise<ApiResponse> {\r\n    return this.request(`/collections/${id}/products/reorder`, {\r\n      method: \"PATCH\",\r\n      body: JSON.stringify({ productOrders }),\r\n    });\r\n  }\r\n\r\n  async createProductVariant(\r\n    productId: string,\r\n    variantData: {\r\n      sku: string;\r\n      name: string;\r\n      description?: string;\r\n      regularPrice: number;\r\n      salePrice?: number;\r\n      weight?: number;\r\n      hsn?: string;\r\n      isActive?: boolean;\r\n      options?: { name: string; value: string }[];\r\n      segmentPrices?: {\r\n        customerType: \"B2C\" | \"B2B\" | \"WHOLESALE\";\r\n        regularPrice: number;\r\n        salePrice?: number;\r\n      }[];\r\n    }\r\n  ): Promise<ApiResponse<ProductVariant>> {\r\n    return this.request<ProductVariant>(`/products/${productId}/variants`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(variantData),\r\n    });\r\n  }\r\n\r\n  async updateProductVariant(\r\n    productId: string,\r\n    variantId: string,\r\n    variantData: {\r\n      sku?: string;\r\n      name?: string;\r\n      description?: string;\r\n      regularPrice?: number;\r\n      salePrice?: number;\r\n      weight?: number;\r\n      hsn?: string;\r\n      isActive?: boolean;\r\n      options?: { name: string; value: string }[];\r\n      segmentPrices?: {\r\n        customerType: \"B2C\" | \"B2B\" | \"WHOLESALE\";\r\n        regularPrice: number;\r\n        salePrice?: number;\r\n      }[];\r\n    }\r\n  ): Promise<ApiResponse<ProductVariant>> {\r\n    return this.request<ProductVariant>(\r\n      `/products/${productId}/variants/${variantId}`,\r\n      {\r\n        method: \"PUT\",\r\n        body: JSON.stringify(variantData),\r\n      }\r\n    );\r\n  }\r\n\r\n  async deleteProductVariant(\r\n    productId: string,\r\n    variantId: string\r\n  ): Promise<ApiResponse<void>> {\r\n    return this.request<void>(`/products/${productId}/variants/${variantId}`, {\r\n      method: \"DELETE\",\r\n    });\r\n  }\r\n\r\n  // Inventory Location endpoints\r\n  async createLocation(data: { name: string; address?: string }) {\r\n    return this.post('/locations', data);\r\n  }\r\n  async updateLocation(id: string, data: { name?: string; address?: string; isActive?: boolean }) {\r\n    return this.put(`/locations/${id}`, data);\r\n  }\r\n  async deleteLocation(id: string) {\r\n    return this.delete(`/locations/${id}`);\r\n  }\r\n\r\n  async initiateOrderRefund(orderId: string, amount: number, reason: string) {\r\n    const token = getToken();\r\n    const response = await fetch(`${this.baseURL}/orders/${orderId}/refund`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),\r\n      },\r\n      body: JSON.stringify({ amount, reason }),\r\n    });\r\n    return response.json();\r\n  }\r\n  async updateRefundStatus(refundId: string, status: string) {\r\n    const token = getToken();\r\n    const response = await fetch(`${this.baseURL}/orders/refunds/${refundId}/status`, {\r\n      method: 'PUT',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),\r\n      },\r\n      body: JSON.stringify({ status }),\r\n    });\r\n    return response.json();\r\n  }\r\n\r\n  // Transaction management endpoints\r\n  async getTransactions(params?: {\r\n    orderId?: string;\r\n    paymentStatus?: string;\r\n    paymentGatewayName?: string;\r\n  }): Promise<ApiResponse<{ transactions: any[] }>> {\r\n    const queryParams = new URLSearchParams();\r\n    if (params?.orderId) queryParams.append('orderId', params.orderId);\r\n    if (params?.paymentStatus) queryParams.append('paymentStatus', params.paymentStatus);\r\n    if (params?.paymentGatewayName) queryParams.append('paymentGatewayName', params.paymentGatewayName);\r\n    return this.get(`/transactions${queryParams.toString() ? `?${queryParams}` : ''}`);\r\n  }\r\n\r\n  async getTransaction(id: string): Promise<ApiResponse<any>> {\r\n    return this.get(`/transactions/${id}`);\r\n  }\r\n\r\n  async createTransaction(data: {\r\n    orderId: string;\r\n    amount: string | number;\r\n    paymentStatus: string;\r\n    paymentGatewayName: string;\r\n    paymentGatewayTransactionId?: string;\r\n    paymentGatewayResponse?: string;\r\n  }): Promise<ApiResponse<any>> {\r\n    return this.post('/transactions', data);\r\n  }\r\n\r\n  // Promotion/Coupon management endpoints\r\n  async getPromotions(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    isActive?: boolean;\r\n  }): Promise<ApiResponse<any>> {\r\n    const searchParams = new URLSearchParams();\r\n    if (params) {\r\n      Object.entries(params).forEach(([key, value]) => {\r\n        if (value !== undefined) {\r\n          searchParams.append(key, value.toString());\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.request(`/promotions?${searchParams.toString()}`);\r\n  }\r\n\r\n  async getPromotion(id: string): Promise<ApiResponse<Promotion>> {\r\n    return this.get(`/promotions/${id}`);\r\n  }\r\n\r\n  // Tax Rates\r\n  async getTaxRates(params?: { country?: string; state?: string }): Promise<ApiResponse<TaxRate[]>> {\r\n    const searchParams = new URLSearchParams();\r\n    if (params) {\r\n      if (params.country) searchParams.append('country', params.country);\r\n      if (params.state) searchParams.append('state', params.state);\r\n    }\r\n\r\n    return this.request(`/tax-rates?${searchParams.toString()}`);\r\n  }\r\n\r\n  async getApplicableTaxRate(country: string, state?: string): Promise<ApiResponse<TaxRate | null>> {\r\n    const searchParams = new URLSearchParams();\r\n    searchParams.append('country', country);\r\n    if (state) searchParams.append('state', state);\r\n\r\n    return this.request(`/tax-rates/applicable?${searchParams.toString()}`);\r\n  }\r\n\r\n  // Shipping Management\r\n  async getShipments(params?: { page?: number; limit?: number; orderId?: string; status?: string }): Promise<ApiResponse<any>> {\r\n    const searchParams = new URLSearchParams();\r\n    if (params) {\r\n      if (params.page) searchParams.append('page', params.page.toString());\r\n      if (params.limit) searchParams.append('limit', params.limit.toString());\r\n      if (params.orderId) searchParams.append('orderId', params.orderId);\r\n      if (params.status) searchParams.append('status', params.status);\r\n    }\r\n\r\n    return this.request(`/shipping?${searchParams.toString()}`);\r\n  }\r\n\r\n  async getShipment(id: string): Promise<ApiResponse<any>> {\r\n    return this.get(`/shipping/${id}`);\r\n  }\r\n\r\n  async getOrderShipments(orderId: string): Promise<ApiResponse<any>> {\r\n    return this.get(`/shipping/order/${orderId}`);\r\n  }\r\n\r\n  async createShipment(data: {\r\n    orderId: string;\r\n    carrier: string;\r\n    trackingNumber?: string;\r\n    trackingUrl?: string;\r\n    status?: string;\r\n  }): Promise<ApiResponse<any>> {\r\n    return this.post('/shipping', data);\r\n  }\r\n\r\n  async updateShipmentTracking(id: string, data: {\r\n    trackingNumber?: string;\r\n    trackingUrl?: string;\r\n    status?: string;\r\n    carrier?: string;\r\n  }): Promise<ApiResponse<any>> {\r\n    return this.put(`/shipping/${id}/tracking`, data);\r\n  }\r\n\r\n  async deleteShipment(id: string): Promise<ApiResponse<any>> {\r\n    return this.delete(`/shipping/${id}`);\r\n  }\r\n\r\n  // Product Relations Management\r\n  async addProductRelation(productId: string, relatedProductId: string, type: 'RELATED' | 'UPSELL' | 'CROSS_SELL'): Promise<ApiResponse<any>> {\r\n    return this.post(`/products/${productId}/relations`, {\r\n      relatedProductId,\r\n      type\r\n    });\r\n  }\r\n\r\n  async removeProductRelation(productId: string, relationId: string): Promise<ApiResponse<any>> {\r\n    return this.delete(`/products/${productId}/relations/${relationId}`);\r\n  }\r\n\r\n  async getProductRelations(productId: string): Promise<ApiResponse<any>> {\r\n    return this.get(`/products/${productId}/relations`);\r\n  }\r\n\r\n  // Product Reviews Management\r\n  async approveReview(reviewId: string): Promise<ApiResponse<any>> {\r\n    return this.put(`/reviews/${reviewId}/approve`, {});\r\n  }\r\n\r\n  async deleteReview(reviewId: string): Promise<ApiResponse<any>> {\r\n    return this.delete(`/reviews/${reviewId}`);\r\n  }\r\n\r\n  async getProductReviews(productId: string): Promise<ApiResponse<any>> {\r\n    return this.get(`/products/${productId}/reviews`);\r\n  }\r\n\r\n  // Inventory Batch Management\r\n  async getInventoryBatches(inventoryId: string): Promise<ApiResponse<any>> {\r\n    return this.get(`/inventory-batches?inventoryId=${inventoryId}`);\r\n  }\r\n\r\n  async createInventoryBatch(data: {\r\n    inventoryId: string;\r\n    batchNumber: string;\r\n    quantity: number;\r\n    expiryDate?: string;\r\n  }): Promise<ApiResponse<any>> {\r\n    return this.post('/inventory-batches', data);\r\n  }\r\n\r\n  async updateInventoryBatch(id: string, data: {\r\n    batchNumber?: string;\r\n    quantity?: number;\r\n    expiryDate?: string;\r\n  }): Promise<ApiResponse<any>> {\r\n    return this.put(`/inventory-batches/${id}`, data);\r\n  }\r\n\r\n  async deleteInventoryBatch(id: string): Promise<ApiResponse<any>> {\r\n    return this.delete(`/inventory-batches/${id}`);\r\n  }\r\n\r\n  async getExpiringBatches(days: number = 30): Promise<ApiResponse<any>> {\r\n    return this.get(`/inventory-batches/expiring?days=${days}`);\r\n  }\r\n\r\n  async getExpiredBatches(): Promise<ApiResponse<any>> {\r\n    return this.get('/inventory-batches/expired');\r\n  }\r\n\r\n  async validateCoupon(code: string): Promise<ApiResponse<Promotion>> {\r\n    return this.get(`/promotions/code/${code}`);\r\n  }\r\n\r\n  async calculatePromotionDiscount(data: {\r\n    promotionCode: string;\r\n    orderItems: Array<{\r\n      variantId: string;\r\n      quantity: number;\r\n      unitPrice: number;\r\n      variant?: { productId?: string };\r\n    }>;\r\n    customerId?: string;\r\n    subtotal: number;\r\n    shippingAmount: number;\r\n  }): Promise<ApiResponse<{ discount: number; appliedItems?: any[] }>> {\r\n    return this.post('/promotions/calculate-discount', data);\r\n  }\r\n\r\n  async createPromotion(data: {\r\n    code: string;\r\n    name: string;\r\n    description?: string;\r\n    type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SHIPPING' | 'BOGO' | 'VOLUME_DISCOUNT';\r\n    value: number;\r\n    minOrderAmount?: number;\r\n    maxDiscount?: number;\r\n    usageLimit?: number;\r\n    startsAt?: string;\r\n    expiresAt?: string;\r\n  }): Promise<ApiResponse<Promotion>> {\r\n    return this.post('/promotions', data);\r\n  }\r\n\r\n  async updatePromotion(id: string, data: Partial<{\r\n    name: string;\r\n    description: string;\r\n    type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SHIPPING' | 'BOGO' | 'VOLUME_DISCOUNT';\r\n    value: number;\r\n    minOrderAmount: number;\r\n    maxDiscount: number;\r\n    usageLimit: number;\r\n    isActive: boolean;\r\n    startsAt: string;\r\n    expiresAt: string;\r\n  }>): Promise<ApiResponse<Promotion>> {\r\n    return this.put(`/promotions/${id}`, data);\r\n  }\r\n\r\n  async deletePromotion(id: string): Promise<ApiResponse<void>> {\r\n    return this.delete(`/promotions/${id}`);\r\n  }\r\n\r\n  async useCoupon(code: string): Promise<ApiResponse<Promotion>> {\r\n    return this.post(`/promotions/use/${code}`);\r\n  }\r\n}\r\n\r\n// Create and export API client instance\r\nexport const api = new ApiClient(API_BASE_URL);\r\n\r\n// Utility functions for common operations\r\nexport const formatCurrency = (amount: number): string => {\r\n  return new Intl.NumberFormat(\"en-US\", {\r\n    style: \"currency\",\r\n    currency: \"USD\",\r\n  }).format(amount);\r\n};\r\n\r\nexport const formatDate = (date: string): string => {\r\n  return new Intl.DateTimeFormat(\"en-US\", {\r\n    year: \"numeric\",\r\n    month: \"short\",\r\n    day: \"numeric\",\r\n    hour: \"2-digit\",\r\n    minute: \"2-digit\",\r\n  }).format(new Date(date));\r\n};\r\n\r\nexport const getStatusColor = (status: string): string => {\r\n  const statusColors: { [key: string]: string } = {\r\n    PENDING: \"bg-yellow-100 text-yellow-800\",\r\n    PROCESSING: \"bg-blue-100 text-blue-800\",\r\n    SHIPPED: \"bg-purple-100 text-purple-800\",\r\n    DELIVERED: \"bg-green-100 text-green-800\",\r\n    CANCELLED: \"bg-red-100 text-red-800\",\r\n    REFUNDED: \"bg-gray-100 text-gray-800\",\r\n    ON_HOLD: \"bg-orange-100 text-orange-800\",\r\n    ACTIVE: \"bg-green-100 text-green-800\",\r\n    INACTIVE: \"bg-gray-100 text-gray-800\",\r\n    DRAFT: \"bg-gray-100 text-gray-800\",\r\n    ARCHIVED: \"bg-red-100 text-red-800\",\r\n  };\r\n  return statusColors[status] || \"bg-gray-100 text-gray-800\";\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEA,MAAM,eACJ,iEAAmC;AAwT9B,MAAM,YAAY;AAElB,MAAM,WAAW;IACtB,wCAAmC,OAAO;;AAE5C;AAEO,MAAM,WAAW,CAAC;IACvB,wCAAmC;;AAErC;AAEO,MAAM,cAAc;IACzB,wCAAmC;;AAErC;AA0BA,mBAAmB;AACnB,MAAM;IACI,QAAgB;IAExB,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEQ,aAA0B;QAChC,MAAM,UAAuB;YAC3B,gBAAgB;QAClB;QAEA,MAAM,QAAQ;QACd,IAAI,OAAO;YACT,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YACzC,QAAQ,GAAG,CAAC,uCAAuC,QAAQ,aAAa;QAC1E,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;IACT;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS,IAAI,CAAC,UAAU;gBACxB,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,mCAAmC;YACnC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B;gBACA,yDAAyD;gBACzD,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MACR,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YAEnE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YAErC,IAAI,iBAAiB,OAAO;gBAC1B,mBAAmB;gBACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;gBAEzB,OAAO;oBACL,SAAS;oBACT,OAAO,MAAM,OAAO;gBACtB;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,IAAa,QAAgB,EAA2B;QAC5D,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAAE,QAAQ;QAAM;IACnD;IACA,MAAM,KAAc,QAAgB,EAAE,IAAU,EAA2B;QACzE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,UAAU,EAAE;gBAAE,gBAAgB;YAAmB;YACpE,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IACA,MAAM,IAAa,QAAgB,EAAE,IAAU,EAA2B;QACxE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,UAAU,EAAE;gBAAE,gBAAgB;YAAmB;YACpE,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IACA,MAAM,OAAgB,QAAgB,EAA2B;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAAE,QAAQ;YAAU,SAAS,IAAI,CAAC,UAAU;QAAG;IAClF;IAEA,2BAA2B;IAC3B,MAAM,MACJ,KAAa,EACb,QAAgB,EACqC;QACrD,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;IACF;IAEA,MAAM,SAAS,QAMd,EAAuD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,aAAyC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,cAAc,QAInB,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,eACJ,eAAuB,EACvB,WAAmB,EACG;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,yBAAyB;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAiB;YAAY;QACtD;IACF;IAEA,MAAM,SAA+B;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB;YAClC,QAAQ;QACV;IACF;IAEA,4BAA4B;IAC5B,MAAM,SAAS,MAMd,EAAoC;QACnC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,aAAa,QAAQ,IAAI;IAGzD;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,IAAI;IACpC;IAEA,MAAM,WAAW,QAOhB,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;YAC5B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WACJ,EAAU,EACV,QAMC,EAC2B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAwB;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;YAClC,QAAQ;QACV;IACF;IAEA,MAAM,kBACJ,EAAU,EACV,WAAmB,EACG;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,eAAe,CAAC,EAAE;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;IACF;IAEA,MAAM,sBACJ,EAAU,EACV,WAAyB,EACG;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;IACF;IAEA,gCAAgC;IAChC,MAAM,aAAa,MAMlB,EAUC;QACA,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAQhC,CAAC,WAAW,EAAE,aAAa,QAAQ,IAAI;QAE1C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,WAAW,SAAS,IAAI,CAAC,SAAS;oBAClC,YAAY,SAAS,IAAI,CAAC,UAAU;gBACtC;YACF;QACF;QAEA,OAAO;IAST;IAEA,MAAM,YAAY,EAAU,EAAkC;QAC5D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI;IACxC;IAEA,MAAM,eAAe,YAUpB,EAAkC;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc;YAChC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,eACJ,EAAU,EACV,YASC,EAC+B;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,eAAe,EAAU,EAAwB;QACrD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE;YACtC,QAAQ;QACV;IACF;IAEA,+BAA+B;IAC/B,MAAM,YAAY,MAajB,EAAgD;QAC/C,MAAM,cAAc,IAAI;QACxB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,UAAU,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;QACpE,IAAI,QAAQ,SAAS,UAAU,SAAS,WAAW;YACjD,YAAY,MAAM,CAAC,WAAW;QAChC;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAQhC,CAAC,UAAU,EAAE,aAAa;QAE7B,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAChC,YAAY,SAAS,IAAI,CAAC,UAAU;gBACtC;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,WAAW,EAAU,EAAiC;QAC1D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,IAAI;IACvC;IAEA,MAAM,cAAc,WAWnB,EAAiC;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cACJ,EAAU,EACV,WAUC,EAC8B;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAwB;QACpD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;YACrC,QAAQ;QACV;IACF;IAEA,6BAA6B;IAC7B,MAAM,UAAU,MAQf,EAA8C;QAC7C,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAQhC,CAAC,QAAQ,EAAE,aAAa,QAAQ,IAAI;QAEvC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,QAAQ,SAAS,IAAI,CAAC,MAAM;oBAC5B,YAAY,SAAS,IAAI,CAAC,UAAU;gBACtC;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,SAAS,EAAU,EAA+B;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI;IACrC;IAEA,MAAM,YAAY,SAajB,EAA+B;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW;YAC7B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,YACJ,EAAU,EACV,SAOC,EAC4B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBACJ,EAAU,EACV,MAAc,EACd,IAAa,EACS;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,EAAE;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAQ;YAAK;QACtC;IACF;IAEA,MAAM,aACJ,EAAU,EACV,IAAY,EACZ,aAAsB,IAAI,EACO;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC,EAAE;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAM;YAAW;QAC1C;IACF;IAEA,MAAM,cAAc,EAAU,EAAqC;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC;IAC3C;IAEA,MAAM,YAAY,EAAU,EAAwB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE;YACnC,QAAQ;QACV;IACF;IAEA,cAAc;IACd,MAAM,WACJ,IAAU,EAC+C;QACzD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,QAAQ;QACd,MAAM,UAAuB,CAAC;QAC9B,IAAI,OAAO;YACT,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;QAC3C;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACrD,QAAQ;gBACR;gBACA,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MACR,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YAEnE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,IAAI,iBAAiB,OAAO;gBAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;gBACzB,OAAO;oBACL,SAAS;oBACT,OAAO,MAAM,OAAO;gBACtB;YACF;YACA,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA,MAAM,cACJ,UAAkB,EAClB,WAAgB,EACe;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,WAAW,UAAU,CAAC,EAAE;YACxD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cACJ,UAAkB,EAClB,SAAiB,EACjB,WAAgB,EACe;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,WAAW,WAAW,EAAE,WAAW,EAAE;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cACJ,UAAkB,EAClB,SAAiB,EACK;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,WAAW,WAAW,EAAE,WAAW,EAAE;YACrE,QAAQ;QACV;IACF;IAEA,uBAAuB;IACvB,MAAM,eAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,aAAa,MAMlB,EA2BC;QACA,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,IAAI,OAAO,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,OAAO,UAAU,EACnB,aAAa,MAAM,CAAC,cAAc,OAAO,UAAU;YACrD,IAAI,OAAO,QAAQ,EACjB,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;QAC5D;QACA,MAAM,cAAc,aAAa,QAAQ;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IACzE;IAEA,MAAM,gBACJ,EAAU,EACV,IAIC,EAC0B;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,wBAAwB,IAa7B,EAA6B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAc,MAInB,EAiBC;QACA,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,IAAI,OAAO,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAChE;QACA,MAAM,cAAc,aAAa,QAAQ;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAC1E;IAEA,MAAM,eAAe,IAAyC,EAS5D;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,eACJ,EAAU,EACV,IAEC,EAUD;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YACvC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,eAAe,EAAU,EAA8B;QAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YACvC,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,MAKpB,EAUC;QACA,MAAM,cAAc,IAAI;QACxB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,OAAO,QAAQ,aAAa,WAC9B,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;QAEzD,OAAO,IAAI,CAAC,OAAO,CAQhB,CAAC,aAAa,EAAE,YAAY,QAAQ,IAAI;IAC7C;IAEA,MAAM,cAAc,EAAU,EAAoC;QAChE,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,aAAa,EAAE,IAAI;IACtD;IAEA,MAAM,iBAAiB,cAMtB,EAAoC;QACnC,OAAO,IAAI,CAAC,OAAO,CAAa,gBAAgB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,iBACJ,EAAU,EACV,cAKC,EACiC;QAClC,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,aAAa,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,iBAAiB,EAAU,EAAwB;QACvD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,yBACJ,EAAU,EACV,UAAoB,EACE;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,GAAG,SAAS,CAAC,EAAE;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAW;QACpC;IACF;IAEA,MAAM,0BACJ,EAAU,EACV,aAA8D,EACxC;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,GAAG,iBAAiB,CAAC,EAAE;YACzD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAc;QACvC;IACF;IAEA,MAAM,qBACJ,SAAiB,EACjB,WAeC,EACqC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAiB,CAAC,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,qBACJ,SAAiB,EACjB,SAAiB,EACjB,WAeC,EACqC;QACtC,OAAO,IAAI,CAAC,OAAO,CACjB,CAAC,UAAU,EAAE,UAAU,UAAU,EAAE,WAAW,EAC9C;YACE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEJ;IAEA,MAAM,qBACJ,SAAiB,EACjB,SAAiB,EACW;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,UAAU,EAAE,UAAU,UAAU,EAAE,WAAW,EAAE;YACxE,QAAQ;QACV;IACF;IAEA,+BAA+B;IAC/B,MAAM,eAAe,IAAwC,EAAE;QAC7D,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc;IACjC;IACA,MAAM,eAAe,EAAU,EAAE,IAA6D,EAAE;QAC9F,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE;IACtC;IACA,MAAM,eAAe,EAAU,EAAE;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IACvC;IAEA,MAAM,oBAAoB,OAAe,EAAE,MAAc,EAAE,MAAc,EAAE;QACzE,MAAM,QAAQ;QACd,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,OAAO,CAAC,EAAE;YACvE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAI,QAAQ;oBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAAC,IAAI,CAAC,CAAC;YACzD;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAQ;YAAO;QACxC;QACA,OAAO,SAAS,IAAI;IACtB;IACA,MAAM,mBAAmB,QAAgB,EAAE,MAAc,EAAE;QACzD,MAAM,QAAQ;QACd,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC,EAAE;YAChF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAI,QAAQ;oBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAAC,IAAI,CAAC,CAAC;YACzD;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAO;QAChC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,MAAM,gBAAgB,MAIrB,EAAiD;QAChD,MAAM,cAAc,IAAI;QACxB,IAAI,QAAQ,SAAS,YAAY,MAAM,CAAC,WAAW,OAAO,OAAO;QACjE,IAAI,QAAQ,eAAe,YAAY,MAAM,CAAC,iBAAiB,OAAO,aAAa;QACnF,IAAI,QAAQ,oBAAoB,YAAY,MAAM,CAAC,sBAAsB,OAAO,kBAAkB;QAClG,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IACnF;IAEA,MAAM,eAAe,EAAU,EAA6B;QAC1D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;IACvC;IAEA,MAAM,kBAAkB,IAOvB,EAA6B;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB;IACpC;IAEA,wCAAwC;IACxC,MAAM,cAAc,MAInB,EAA6B;QAC5B,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,aAAa,QAAQ,IAAI;IAC9D;IAEA,MAAM,aAAa,EAAU,EAAmC;QAC9D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;IACrC;IAEA,YAAY;IACZ,MAAM,YAAY,MAA6C,EAAmC;QAChG,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,IAAI,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;YACjE,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK;QAC7D;QAEA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,aAAa,QAAQ,IAAI;IAC7D;IAEA,MAAM,qBAAqB,OAAe,EAAE,KAAc,EAAwC;QAChG,MAAM,eAAe,IAAI;QACzB,aAAa,MAAM,CAAC,WAAW;QAC/B,IAAI,OAAO,aAAa,MAAM,CAAC,SAAS;QAExC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,aAAa,QAAQ,IAAI;IACxE;IAEA,sBAAsB;IACtB,MAAM,aAAa,MAA6E,EAA6B;QAC3H,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,IAAI,OAAO,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;YACjE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAChE;QAEA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,aAAa,QAAQ,IAAI;IAC5D;IAEA,MAAM,YAAY,EAAU,EAA6B;QACvD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IACnC;IAEA,MAAM,kBAAkB,OAAe,EAA6B;QAClE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS;IAC9C;IAEA,MAAM,eAAe,IAMpB,EAA6B;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa;IAChC;IAEA,MAAM,uBAAuB,EAAU,EAAE,IAKxC,EAA6B;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,SAAS,CAAC,EAAE;IAC9C;IAEA,MAAM,eAAe,EAAU,EAA6B;QAC1D,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IACtC;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,SAAiB,EAAE,gBAAwB,EAAE,IAAyC,EAA6B;QAC1I,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,UAAU,CAAC,EAAE;YACnD;YACA;QACF;IACF;IAEA,MAAM,sBAAsB,SAAiB,EAAE,UAAkB,EAA6B;QAC5F,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,UAAU,WAAW,EAAE,YAAY;IACrE;IAEA,MAAM,oBAAoB,SAAiB,EAA6B;QACtE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,UAAU,CAAC;IACpD;IAEA,6BAA6B;IAC7B,MAAM,cAAc,QAAgB,EAA6B;QAC/D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,QAAQ,CAAC,EAAE,CAAC;IACnD;IAEA,MAAM,aAAa,QAAgB,EAA6B;QAC9D,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;IAC3C;IAEA,MAAM,kBAAkB,SAAiB,EAA6B;QACpE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;IAClD;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB,WAAmB,EAA6B;QACxE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,+BAA+B,EAAE,aAAa;IACjE;IAEA,MAAM,qBAAqB,IAK1B,EAA6B;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,sBAAsB;IACzC;IAEA,MAAM,qBAAqB,EAAU,EAAE,IAItC,EAA6B;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,IAAI,EAAE;IAC9C;IAEA,MAAM,qBAAqB,EAAU,EAA6B;QAChE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,mBAAmB,EAAE,IAAI;IAC/C;IAEA,MAAM,mBAAmB,OAAe,EAAE,EAA6B;QACrE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,MAAM;IAC5D;IAEA,MAAM,oBAA+C;QACnD,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB;IAEA,MAAM,eAAe,IAAY,EAAmC;QAClE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM;IAC5C;IAEA,MAAM,2BAA2B,IAWhC,EAAoE;QACnE,OAAO,IAAI,CAAC,IAAI,CAAC,kCAAkC;IACrD;IAEA,MAAM,gBAAgB,IAWrB,EAAmC;QAClC,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe;IAClC;IAEA,MAAM,gBAAgB,EAAU,EAAE,IAWhC,EAAmC;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;IACvC;IAEA,MAAM,gBAAgB,EAAU,EAA8B;QAC5D,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;IACxC;IAEA,MAAM,UAAU,IAAY,EAAmC;QAC7D,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,MAAM;IAC5C;AACF;AAGO,MAAM,MAAM,IAAI,UAAU;AAG1B,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,eAA0C;QAC9C,SAAS;QACT,YAAY;QACZ,SAAS;QACT,WAAW;QACX,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA,OAAO,YAAY,CAAC,OAAO,IAAI;AACjC", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/contexts/auth-context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\r\nimport { api, User, getToken, setToken, removeToken } from '@/lib/api';\r\nimport { useRouter } from 'next/navigation';\r\nimport { toast } from 'sonner';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  isLoading: boolean;\r\n  isAuthenticated: boolean;\r\n  login: (email: string, password: string) => Promise<boolean>;\r\n  logout: () => void;\r\n  register: (userData: {\r\n    email: string;\r\n    password: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    role?: string;\r\n  }) => Promise<boolean>;\r\n  refreshUser: () => Promise<void>;\r\n  hasPermission: (module: string, action: string) => boolean;\r\n  hasRole: (roles: string | string[]) => boolean;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const router = useRouter();\r\n\r\n  const isAuthenticated = !!user;\r\n\r\n  // Initialize auth state\r\n  useEffect(() => {\r\n    const initializeAuth = async () => {\r\n      const token = getToken();\r\n      \r\n      if (token) {\r\n        try {\r\n          const response = await api.getProfile();\r\n          if (response.success && response.data) {\r\n            setUser(response.data);\r\n          } else {\r\n            // Invalid token, remove it\r\n            removeToken();\r\n          }\r\n        } catch (error) {\r\n          console.error('Failed to initialize auth:', error);\r\n          removeToken();\r\n        }\r\n      }\r\n      \r\n      setIsLoading(false);\r\n    };\r\n\r\n    initializeAuth();\r\n  }, []);\r\n\r\n  const login = async (email: string, password: string): Promise<boolean> => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await api.login(email, password);\r\n      \r\n      if (response.success && response.data) {\r\n        setToken(response.data.token);\r\n        console.log('[Auth] Token set after login:', response.data.token);\r\n        setUser(response.data.user);\r\n        toast.success('Login successful');\r\n        return true;\r\n      } else {\r\n        toast.error(response.error || 'Login failed');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      toast.error('Login failed');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const register = async (userData: {\r\n    email: string;\r\n    password: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    role?: string;\r\n  }): Promise<boolean> => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await api.register(userData);\r\n      \r\n      if (response.success && response.data) {\r\n        setToken(response.data.token);\r\n        setUser(response.data.user);\r\n        toast.success('Registration successful');\r\n        return true;\r\n      } else {\r\n        toast.error(response.error || 'Registration failed');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Registration error:', error);\r\n      toast.error('Registration failed');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    try {\r\n      // Call logout API (but don't wait for it)\r\n      api.logout().catch(console.error);\r\n      \r\n      // Clear local state\r\n      removeToken();\r\n      setUser(null);\r\n      \r\n      toast.success('Logged out successfully');\r\n      router.push('/login');\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    }\r\n  };\r\n\r\n  const refreshUser = async (): Promise<void> => {\r\n    try {\r\n      const response = await api.getProfile();\r\n      if (response.success && response.data) {\r\n        setUser(response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to refresh user:', error);\r\n    }\r\n  };\r\n\r\n  const hasPermission = (module: string, action: string): boolean => {\r\n    if (!user) return false;\r\n    \r\n    // Admins have all permissions\r\n    if (user.role === 'ADMIN') return true;\r\n    \r\n    // Check specific permissions\r\n    if (user.permissions) {\r\n      return user.permissions.some(\r\n        permission => \r\n          permission.module === module && \r\n          permission.action === action && \r\n          permission.granted\r\n      );\r\n    }\r\n    \r\n    return false;\r\n  };\r\n\r\n  const hasRole = (roles: string | string[]): boolean => {\r\n    if (!user) return false;\r\n    \r\n    const roleArray = Array.isArray(roles) ? roles : [roles];\r\n    return roleArray.includes(user.role);\r\n  };\r\n\r\n  const value: AuthContextType = {\r\n    user,\r\n    isLoading,\r\n    isAuthenticated,\r\n    login,\r\n    logout,\r\n    register,\r\n    refreshUser,\r\n    hasPermission,\r\n    hasRole,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\n// Protected Route Component\r\ninterface ProtectedRouteProps {\r\n  children: ReactNode;\r\n  requiredRoles?: string[];\r\n  requiredPermissions?: Array<{ module: string; action: string }>;\r\n  fallback?: ReactNode;\r\n}\r\n\r\nexport const ProtectedRoute: React.FC<ProtectedRouteProps> = ({\r\n  children,\r\n  requiredRoles,\r\n  requiredPermissions,\r\n  fallback,\r\n}) => {\r\n  const { user, isLoading, isAuthenticated, hasRole, hasPermission } = useAuth();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    // Only redirect if we're not loading and the user is definitely not authenticated\r\n    if (!isLoading && !isAuthenticated) {\r\n      router.push('/login');\r\n    }\r\n  }, [isLoading, isAuthenticated, router]);\r\n\r\n  // Show loading state while checking authentication\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // If not authenticated and not loading, show fallback or null\r\n  if (!isAuthenticated || !user) {\r\n    return fallback || null;\r\n  }\r\n\r\n  // Check role requirements\r\n  if (requiredRoles && !hasRole(requiredRoles)) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <div className=\"text-center\">\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Access Denied</h2>\r\n          <p className=\"text-gray-600\">You don't have the required role to access this page.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Check permission requirements\r\n  if (requiredPermissions) {\r\n    const hasAllPermissions = requiredPermissions.every(\r\n      ({ module, action }) => hasPermission(module, action)\r\n    );\r\n\r\n    if (!hasAllPermissions) {\r\n      return (\r\n        <div className=\"flex items-center justify-center min-h-screen\">\r\n          <div className=\"text-center\">\r\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Access Denied</h2>\r\n            <p className=\"text-gray-600\">You don't have the required permissions to access this page.</p>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n  }\r\n\r\n  // All checks passed, render the protected content\r\n  return <>{children}</>;\r\n};\r\n\r\n// Hook for checking permissions in components\r\nexport const usePermissions = () => {\r\n  const { hasPermission, hasRole, user } = useAuth();\r\n  \r\n  return {\r\n    hasPermission,\r\n    hasRole,\r\n    user,\r\n    canCreate: (module: string) => hasPermission(module, 'CREATE'),\r\n    canRead: (module: string) => hasPermission(module, 'READ'),\r\n    canUpdate: (module: string) => hasPermission(module, 'UPDATE'),\r\n    canDelete: (module: string) => hasPermission(module, 'DELETE'),\r\n    isAdmin: () => hasRole('ADMIN'),\r\n    isManager: () => hasRole(['ADMIN', 'MANAGER']),\r\n    isStaff: () => hasRole(['ADMIN', 'MANAGER', 'STAFF']),\r\n  };\r\n};"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAyBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,CAAC,CAAC;IAE1B,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,MAAM,QAAQ,CAAA,GAAA,iHAAA,CAAA,WAAQ,AAAD;YAErB,IAAI,OAAO;gBACT,IAAI;oBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,UAAU;oBACrC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,QAAQ,SAAS,IAAI;oBACvB,OAAO;wBACL,2BAA2B;wBAC3B,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD;oBACZ;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD;gBACZ;YACF;YAEA,aAAa;QACf;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,OAAO;YAExC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,CAAA,GAAA,iHAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,IAAI,CAAC,KAAK;gBAC5B,QAAQ,GAAG,CAAC,iCAAiC,SAAS,IAAI,CAAC,KAAK;gBAChE,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI;gBAC9B,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QAOtB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC;YAEpC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,CAAA,GAAA,iHAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,IAAI,CAAC,KAAK;gBAC5B,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI;gBAC9B,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,0CAA0C;YAC1C,iHAAA,CAAA,MAAG,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,KAAK;YAEhC,oBAAoB;YACpB,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD;YACV,QAAQ;YAER,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,UAAU;YACrC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,gBAAgB,CAAC,QAAgB;QACrC,IAAI,CAAC,MAAM,OAAO;QAElB,8BAA8B;QAC9B,IAAI,KAAK,IAAI,KAAK,SAAS,OAAO;QAElC,6BAA6B;QAC7B,IAAI,KAAK,WAAW,EAAE;YACpB,OAAO,KAAK,WAAW,CAAC,IAAI,CAC1B,CAAA,aACE,WAAW,MAAM,KAAK,UACtB,WAAW,MAAM,KAAK,UACtB,WAAW,OAAO;QAExB;QAEA,OAAO;IACT;IAEA,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,YAAY,MAAM,OAAO,CAAC,SAAS,QAAQ;YAAC;SAAM;QACxD,OAAO,UAAU,QAAQ,CAAC,KAAK,IAAI;IACrC;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAUO,MAAM,iBAAgD,CAAC,EAC5D,QAAQ,EACR,aAAa,EACb,mBAAmB,EACnB,QAAQ,EACT;IACC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG;IACrE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kFAAkF;QAClF,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAW;QAAiB;KAAO;IAEvC,mDAAmD;IACnD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,8DAA8D;IAC9D,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,OAAO,YAAY;IACrB;IAEA,0BAA0B;IAC1B,IAAI,iBAAiB,CAAC,QAAQ,gBAAgB;QAC5C,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,gCAAgC;IAChC,IAAI,qBAAqB;QACvB,MAAM,oBAAoB,oBAAoB,KAAK,CACjD,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAK,cAAc,QAAQ;QAGhD,IAAI,CAAC,mBAAmB;YACtB,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;QAIrC;IACF;IAEA,kDAAkD;IAClD,qBAAO;kBAAG;;AACZ;AAGO,MAAM,iBAAiB;IAC5B,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;IAEzC,OAAO;QACL;QACA;QACA;QACA,WAAW,CAAC,SAAmB,cAAc,QAAQ;QACrD,SAAS,CAAC,SAAmB,cAAc,QAAQ;QACnD,WAAW,CAAC,SAAmB,cAAc,QAAQ;QACrD,WAAW,CAAC,SAAmB,cAAc,QAAQ;QACrD,SAAS,IAAM,QAAQ;QACvB,WAAW,IAAM,QAAQ;gBAAC;gBAAS;aAAU;QAC7C,SAAS,IAAM,QAAQ;gBAAC;gBAAS;gBAAW;aAAQ;IACtD;AACF", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useTheme } from \"next-themes\"\r\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = \"system\" } = useTheme()\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps[\"theme\"]}\r\n      className=\"toaster group\"\r\n      style={\r\n        {\r\n          \"--normal-bg\": \"var(--popover)\",\r\n          \"--normal-text\": \"var(--popover-foreground)\",\r\n          \"--normal-border\": \"var(--border)\",\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}]}