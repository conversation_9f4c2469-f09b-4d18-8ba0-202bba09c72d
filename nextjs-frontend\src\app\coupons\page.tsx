'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/dashboard/dashboard-layout';
import { ProtectedRoute } from '@/contexts/auth-context';
import { CouponsTable } from '@/components/coupons/coupons-table';
import { CreateCouponDialog } from '@/components/coupons/create-coupon-dialog';
import { EditCouponDialog } from '@/components/coupons/edit-coupon-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Plus, 
  Search, 
  Tag, 
  TrendingUp, 
  Users, 
  DollarSign,
  Percent
} from 'lucide-react';
import { api, Promotion } from '@/lib/api';
import { toast } from 'sonner';

export default function CouponsPage() {
  const [coupons, setCoupons] = useState<Promotion[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCoupons, setTotalCoupons] = useState(0);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingCoupon, setEditingCoupon] = useState<Promotion | null>(null);

  // Stats
  const [stats, setStats] = useState({
    totalCoupons: 0,
    activeCoupons: 0,
    totalUsage: 0,
    totalDiscount: 0,
  });

  useEffect(() => {
    fetchCoupons();
  }, [currentPage, statusFilter, typeFilter]);

  const fetchCoupons = async () => {
    try {
      setLoading(true);
      const response = await api.getPromotions({
        page: currentPage,
        limit: 10,
        isActive: statusFilter === 'active' ? true : statusFilter === 'inactive' ? false : undefined,
      });

      console.log('Promotions API Response:', response);

      if (response.success && response.data) {
        // The backend returns promotions directly in response.data, not response.data.data
        const promotions = Array.isArray(response.data) ? response.data : response.data.data || [];
        const pagination = response.pagination || response.data.pagination || {};

        console.log('Promotions:', promotions);
        console.log('Pagination:', pagination);

        setCoupons(promotions);
        setTotalPages(pagination.pages || 1);
        setTotalCoupons(pagination.total || 0);

        // Calculate stats
        setStats({
          totalCoupons: promotions.length,
          activeCoupons: promotions.filter(c => c.isActive).length,
          totalUsage: promotions.reduce((sum, c) => sum + c.usageCount, 0),
          totalDiscount: promotions.reduce((sum, c) => sum + (c.usageCount * parseFloat(c.value.toString())), 0),
        });
      } else {
        console.error('Failed to fetch promotions:', response);
        toast.error(response.error || 'Failed to fetch coupons');
      }
    } catch (error) {
      console.error('Failed to fetch coupons:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCouponCreated = () => {
    setShowCreateDialog(false);
    fetchCoupons();
    toast.success('Coupon created successfully');
  };

  const handleCouponUpdated = () => {
    setEditingCoupon(null);
    fetchCoupons();
    toast.success('Coupon updated successfully');
  };

  const handleEditCoupon = async (coupon: Promotion) => {
    try {
      // Fetch full coupon data including volume tiers, product rules, etc.
      const response = await api.getPromotion(coupon.id);

      if (response.success) {
        setEditingCoupon(response.data);
      } else {
        toast.error('Failed to load coupon details');
      }
    } catch (error) {
      console.error('Failed to fetch coupon details:', error);
      toast.error('Failed to load coupon details');
    }
  };

  const handleDeleteCoupon = async (coupon: Promotion) => {
    if (!confirm(`Are you sure you want to delete the coupon "${coupon.code}"?`)) {
      return;
    }

    try {
      const response = await api.deletePromotion(coupon.id);
      if (response.success) {
        toast.success('Coupon deleted successfully');
        fetchCoupons();
      } else {
        toast.error(response.error || 'Failed to delete coupon');
      }
    } catch (error) {
      console.error('Failed to delete coupon:', error);
      toast.error('An unexpected error occurred');
    }
  };

  const filteredCoupons = coupons.filter(coupon => {
    const matchesSearch = searchTerm === '' ||
      coupon.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      coupon.name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === '' || typeFilter === 'all' || coupon.type === typeFilter;

    return matchesSearch && matchesType;
  });

  return (
    <ProtectedRoute requiredRoles={['ADMIN', 'MANAGER']}>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Coupons & Discounts</h1>
              <p className="text-muted-foreground">
                Manage promotional codes and discount campaigns.
              </p>
            </div>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Coupon
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Coupons</CardTitle>
                <Tag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalCoupons}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.activeCoupons} active
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Coupons</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.activeCoupons}</div>
                <p className="text-xs text-muted-foreground">
                  Currently available
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUsage}</div>
                <p className="text-xs text-muted-foreground">
                  Times used
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Savings</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${stats.totalDiscount.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground">
                  Customer savings
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
              <CardDescription>
                Filter and search through your coupons.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 md:flex-row md:items-center">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search coupons..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                    <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                    <SelectItem value="FREE_SHIPPING">Free Shipping</SelectItem>
                    <SelectItem value="BOGO">Buy One Get One</SelectItem>
                    <SelectItem value="VOLUME_DISCOUNT">Volume Discount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Coupons Table */}
          <Card>
            <CardHeader>
              <CardTitle>Coupons</CardTitle>
              <CardDescription>
                A list of all your promotional codes and their performance.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CouponsTable
                coupons={filteredCoupons}
                loading={loading}
                onEdit={handleEditCoupon}
                onDelete={handleDeleteCoupon}
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </CardContent>
          </Card>

          {/* Dialogs */}
          <CreateCouponDialog
            open={showCreateDialog}
            onOpenChange={setShowCreateDialog}
            onSuccess={handleCouponCreated}
          />
          
          <EditCouponDialog
            coupon={editingCoupon}
            open={!!editingCoupon}
            onOpenChange={(open) => !open && setEditingCoupon(null)}
            onSuccess={handleCouponUpdated}
          />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
