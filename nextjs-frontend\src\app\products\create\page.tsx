'use client';

import { useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/dashboard-layout';
import { ProtectedRoute } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Plus, X, Upload, Package, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import { api } from '@/lib/api';

interface ProductFormData {
  name: string;
  description: string;
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE';
  categories: string[];
  tags: string[];
  images: { url: string; altText: string; sortOrder: number }[];
  seoTitle?: string;
  seoDescription?: string;
  seoSlug?: string;
  variants: {
    sku: string;
    name: string;
    description: string;
    regularPrice: string;
    salePrice: string;
    weight: string;
    hsn: string;
    isActive: boolean;
    seoTitle: string;
    seoDescription: string;
    seoSlug: string;
    variantOptions: { name: string; value: string }[];
  }[];
}

interface FormErrors {
  name?: string;
  description?: string;
  variants?: string;
}

export default function CreateProductPage() {
  const router = useRouter();
  const [currentTab, setCurrentTab] = useState('basic');
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    status: 'DRAFT',
    categories: [],
    tags: [],
    images: [],
    seoTitle: '',
    seoDescription: '',
    seoSlug: '',
    variants: [{
      sku: '',
      name: '',
      description: '',
      regularPrice: '',
      salePrice: '',
      weight: '',
      hsn: '',
      isActive: true,
      seoTitle: '',
      seoDescription: '',
      seoSlug: '',
      variantOptions: [],
    }],
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newCategory, setNewCategory] = useState('');
  const [newTag, setNewTag] = useState('');
  const [uploadingImage, setUploadingImage] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Product description is required';
    }

    if (formData.variants.length === 0) {
      newErrors.variants = 'At least one variant is required';
    } else {
      const hasValidVariant = formData.variants.some(variant => 
        variant.sku.trim() && variant.name.trim() && variant.regularPrice.trim()
      );
      if (!hasValidVariant) {
        newErrors.variants = 'At least one variant must have SKU, name, and regular price';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const productData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        status: formData.status,
        categories: formData.categories,
        tags: formData.tags,
        images: formData.images,
        seoTitle: formData.seoTitle?.trim() || undefined,
        seoDescription: formData.seoDescription?.trim() || undefined,
        seoSlug: formData.seoSlug?.trim() || undefined,
        variants: formData.variants.filter(variant => 
          variant.sku.trim() && variant.name.trim()
        ).map(variant => ({
          ...variant,
          regularPrice: parseFloat(variant.regularPrice) || 0,
          salePrice: variant.salePrice ? parseFloat(variant.salePrice) : null,
          weight: variant.weight ? parseFloat(variant.weight) : null,
        })),
      };

      const response = await api.createProduct(productData);

      if (response.success) {
        toast.success('Product created successfully');
        router.push('/products');
      } else {
        toast.error(response.error || 'Failed to create product');
      }
    } catch (error) {
      console.error('Failed to create product:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addVariant = () => {
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, {
        sku: '',
        name: '',
        description: '',
        regularPrice: '',
        salePrice: '',
        weight: '',
        hsn: '',
        isActive: true,
        seoTitle: '',
        seoDescription: '',
        seoSlug: '',
        variantOptions: [],
      }],
    }));
  };

  const removeVariant = (index: number) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter((_, i) => i !== index),
    }));
  };

  const updateVariant = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.map((variant, i) => 
        i === index ? { ...variant, [field]: value } : variant
      ),
    }));
  };

  const addCategory = () => {
    if (newCategory.trim() && !formData.categories.includes(newCategory.trim())) {
      setFormData(prev => ({
        ...prev,
        categories: [...prev.categories, newCategory.trim()],
      }));
      setNewCategory('');
    }
  };

  const removeCategory = (category: string) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.filter(c => c !== category),
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag),
    }));
  };

  const tabs = ['basic', 'variants', 'images', 'categories'];

  const nextTab = () => {
    const currentIndex = tabs.indexOf(currentTab);
    if (currentIndex < tabs.length - 1) {
      setCurrentTab(tabs[currentIndex + 1]);
    }
  };

  const prevTab = () => {
    const currentIndex = tabs.indexOf(currentTab);
    if (currentIndex > 0) {
      setCurrentTab(tabs[currentIndex - 1]);
    }
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files[0]);
    }
  }, []);

  const handleImageUpload = async (file: File) => {
    setUploadingImage(true);
    try {
      // Simulate image upload - replace with actual upload logic
      const imageUrl = URL.createObjectURL(file);
      const newImage = {
        url: imageUrl,
        altText: file.name,
        sortOrder: formData.images.length,
      };

      setFormData(prev => ({
        ...prev,
        images: [...prev.images, newImage],
      }));

      toast.success('Image uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload image');
    } finally {
      setUploadingImage(false);
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  // Tab navigation helpers
  const isFirstTab = currentTab === tabs[0];
  const isLastTab = currentTab === tabs[tabs.length - 1];

  return (
    <ProtectedRoute requiredRoles={['ADMIN', 'MANAGER']}>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/products')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
              </Button>
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Create New Product</h1>
                <p className="text-muted-foreground">
                  Add a new product to your catalog with variants and details.
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <Card>
            <CardContent className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">Basic Info</TabsTrigger>
                    <TabsTrigger value="variants">Variants</TabsTrigger>
                    <TabsTrigger value="images">Images</TabsTrigger>
                    <TabsTrigger value="categories">Categories</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Product Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter product name"
                        className={errors.name ? 'border-red-500' : ''}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-600">{errors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter product description"
                        className={errors.description ? 'border-red-500' : ''}
                        rows={4}
                      />
                      {errors.description && (
                        <p className="text-sm text-red-600">{errors.description}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={formData.status}
                        onValueChange={(value: 'DRAFT' | 'ACTIVE' | 'INACTIVE') => 
                          setFormData(prev => ({ ...prev, status: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="DRAFT">Draft</SelectItem>
                          <SelectItem value="ACTIVE">Active</SelectItem>
                          <SelectItem value="INACTIVE">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TabsContent>

                  <TabsContent value="variants" className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Product Variants</h3>
                      <Button type="button" onClick={addVariant} size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Variant
                      </Button>
                    </div>

                    {errors.variants && (
                      <p className="text-sm text-red-600">{errors.variants}</p>
                    )}

                    <div className="space-y-4">
                      {formData.variants.map((variant, index) => (
                        <Card key={index}>
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-sm">Variant {index + 1}</CardTitle>
                              {formData.variants.length > 1 && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeVariant(index)}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>SKU</Label>
                                <Input
                                  value={variant.sku}
                                  onChange={(e) => updateVariant(index, 'sku', e.target.value)}
                                  placeholder="Enter SKU"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Variant Name</Label>
                                <Input
                                  value={variant.name}
                                  onChange={(e) => updateVariant(index, 'name', e.target.value)}
                                  placeholder="Enter variant name"
                                />
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label>Description</Label>
                              <Textarea
                                value={variant.description}
                                onChange={(e) => updateVariant(index, 'description', e.target.value)}
                                placeholder="Enter variant description"
                                rows={2}
                              />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Regular Price</Label>
                                <Input
                                  type="number"
                                  step="0.01"
                                  value={variant.regularPrice}
                                  onChange={(e) => updateVariant(index, 'regularPrice', e.target.value)}
                                  placeholder="0.00"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Sale Price</Label>
                                <Input
                                  type="number"
                                  step="0.01"
                                  value={variant.salePrice}
                                  onChange={(e) => updateVariant(index, 'salePrice', e.target.value)}
                                  placeholder="0.00"
                                />
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Weight (g)</Label>
                                <Input
                                  type="number"
                                  step="0.01"
                                  value={variant.weight}
                                  onChange={(e) => updateVariant(index, 'weight', e.target.value)}
                                  placeholder="0.00"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>HSN Code</Label>
                                <Input
                                  value={variant.hsn}
                                  onChange={(e) => updateVariant(index, 'hsn', e.target.value)}
                                  placeholder="Enter HSN code"
                                />
                              </div>
                            </div>

                            {/* SEO Fields for Variant */}
                            <div className="border-t pt-4">
                              <h4 className="font-medium mb-3">SEO Information for this variant</h4>
                              <div className="space-y-4">
                                <div className="space-y-2">
                                  <Label>SEO Title</Label>
                                  <Input
                                    value={variant.seoTitle}
                                    onChange={(e) => updateVariant(index, 'seoTitle', e.target.value)}
                                    placeholder="SEO title for this variant"
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label>SEO Description</Label>
                                  <Textarea
                                    value={variant.seoDescription}
                                    onChange={(e) => updateVariant(index, 'seoDescription', e.target.value)}
                                    placeholder="SEO description for this variant"
                                    rows={2}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label>SEO Slug</Label>
                                  <Input
                                    value={variant.seoSlug}
                                    onChange={(e) => updateVariant(index, 'seoSlug', e.target.value)}
                                    placeholder="SEO-friendly URL slug (e.g. variant-name)"
                                  />
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={`variant-active-${index}`}
                                checked={variant.isActive}
                                onChange={(e) => updateVariant(index, 'isActive', e.target.checked)}
                                className="rounded border-gray-300"
                              />
                              <Label htmlFor={`variant-active-${index}`}>Active</Label>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="images" className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">Product Images</h3>
                      <div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handleImageUpload(file);
                            }
                          }}
                          className="hidden"
                          ref={fileInputRef}
                          id="image-upload"
                        />
                        <Button
                          type="button"
                          size="sm"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={uploadingImage}
                          className="flex items-center gap-2"
                        >
                          <Upload className="h-4 w-4" />
                          Upload Image
                        </Button>
                      </div>
                    </div>

                    <div
                      className={`border-2 border-dashed rounded-lg p-8 flex flex-col items-center justify-center transition-colors ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-muted'}`}
                      onDragEnter={handleDrag}
                      onDragOver={handleDrag}
                      onDragLeave={handleDrag}
                      onDrop={handleDrop}
                      style={{ minHeight: '180px', cursor: 'pointer' }}
                      onClick={() => fileInputRef.current?.click()}
                    >
                      {formData.images.length === 0 ? (
                        <>
                          <Package className="h-12 w-12 mb-2 text-muted-foreground opacity-60" />
                          <div className="text-muted-foreground mb-1">No images uploaded</div>
                          <div className="text-xs text-muted-foreground">Click "Upload Image" or drag and drop to add product images</div>
                        </>
                      ) : (
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 w-full">
                          {formData.images.map((image, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={image.url}
                                alt={image.altText}
                                className="w-full h-32 object-cover rounded-lg border"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={e => { e.stopPropagation(); removeImage(index); }}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                      {uploadingImage && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white/60 z-10">
                          <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="categories" className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium mb-2">Categories</h3>
                        <div className="flex gap-2">
                          <Input
                            value={newCategory}
                            onChange={(e) => setNewCategory(e.target.value)}
                            placeholder="Add category"
                            onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCategory())}
                          />
                          <Button type="button" onClick={addCategory} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {formData.categories.map((category) => (
                            <Badge key={category} variant="secondary" className="flex items-center gap-1">
                              {category}
                              <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => removeCategory(category)}
                              />
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-medium mb-2">Tags</h3>
                        <div className="flex gap-2">
                          <Input
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            placeholder="Add tag"
                            onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                          />
                          <Button type="button" onClick={addTag} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {formData.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="flex items-center gap-1">
                              {tag}
                              <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => removeTag(tag)}
                              />
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  {/* Navigation Buttons */}
                  <div className="flex items-center justify-end gap-4 pt-6 border-t mt-6">
                    {!isFirstTab && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={prevTab}
                        disabled={isSubmitting}
                      >
                        Back
                      </Button>
                    )}
                    {!isLastTab ? (
                      <Button
                        type="button"
                        onClick={nextTab}
                        disabled={isSubmitting}
                      >
                        Save & Next
                      </Button>
                    ) : (
                      <>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => router.push('/products')}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                        <Button type="submit" disabled={isSubmitting}>
                          {isSubmitting ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Creating...
                            </>
                          ) : (
                            'Create Product'
                          )}
                        </Button>
                      </>
                    )}
                  </div>
                </Tabs>
              </form>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
