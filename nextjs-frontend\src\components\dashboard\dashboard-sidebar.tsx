"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
    LayoutDashboard,
    ShoppingCart,
    Package,
    Users,
    BarChart3,
    Settings,
    Gift,
    Truck,
    CreditCard,
    FileText,
    Bell,
    ChevronDown,
    ChevronRight,
    Home,
    Tag,
    Warehouse,
    MessageSquare,
    Mail,
    X
} from "lucide-react";

interface DashboardSidebarProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

interface NavItem {
    title: string;
    href: string;
    icon: React.ComponentType<any>;
    badge?: string;
    children?: NavItem[];
}

const navItems: NavItem[] = [
    {
        title: "Dashboard",
        href: "/",
        icon: LayoutDashboard,
    },
    {
        title: "Orders",
        href: "/orders",
        icon: ShoppingCart,
        badge: "23",
        children: [
            { title: "All Orders", href: "/orders", icon: ShoppingCart },
            { title: "Pending", href: "/orders/pending", icon: ShoppingCart },
            { title: "Processing", href: "/orders/processing", icon: ShoppingCart },
            { title: "Shipped", href: "/orders/shipped", icon: ShoppingCart },
            { title: "Delivered", href: "/orders/delivered", icon: ShoppingCart },
            { title: "Cancelled", href: "/orders/cancelled", icon: ShoppingCart },
        ]
    },
    {
        title: "Products",
        href: "/products",
        icon: Package,
        children: [
            { title: "All Products", href: "/products", icon: Package },
            { title: "Categories", href: "/products/categories", icon: Tag },
            { title: "Collections", href: "/products/collections", icon: Package },
            { title: "Inventory", href: "/products/inventory", icon: Warehouse },
        ]
    },
    {
        title: "Customers",
        href: "/customers",
        icon: Users,
        children: [
            { title: "All Customers", href: "/customers", icon: Users },
            { title: "B2C Customers", href: "/customers/b2c", icon: Users },
            { title: "B2B Customers", href: "/customers/b2b", icon: Users },
            { title: "Enterprise", href: "/customers/enterprise", icon: Users },
        ]
    },
    {
        title: "Analytics",
        href: "/analytics",
        icon: BarChart3,
        children: [
            { title: "Overview", href: "/analytics", icon: BarChart3 },
            { title: "Sales Reports", href: "/analytics/sales", icon: BarChart3 },
            { title: "Product Performance", href: "/analytics/products", icon: Package },
            { title: "Customer Insights", href: "/analytics/customers", icon: Users },
        ]
    },
    {
        title: "Marketing",
        href: "/marketing",
        icon: MessageSquare,
        children: [
            { title: "Campaigns", href: "/marketing/campaigns", icon: MessageSquare },
            { title: "Email Marketing", href: "/marketing/email", icon: Mail },
            { title: "Promotions", href: "/marketing/promotions", icon: Gift },
            { title: "Loyalty Program", href: "/marketing/loyalty", icon: Gift },
        ]
    },
    {
        title: "Promotions",
        href: "/coupons",
        icon: Tag,
    },
    {
        title: "Payments",
        href: "/payments",
        icon: CreditCard,
    },
    {
        title: "Shipping",
        href: "/shipping",
        icon: Truck,
    },
    {
        title: "Content",
        href: "/content",
        icon: FileText,
        children: [
            { title: "Pages", href: "/content/pages", icon: FileText },
            { title: "Blog", href: "/content/blog", icon: FileText },
            { title: "Navigation", href: "/content/navigation", icon: FileText },
            { title: "Media", href: "/content/media", icon: FileText },
        ]
    },
    {
        title: "Users",
        href: "/users",
        icon: Users,
    },
    {
        title: "Settings",
        href: "/settings",
        icon: Settings,
        children: [
            { title: "General", href: "/settings/general", icon: Settings },
            { title: "Payments", href: "/settings/payments", icon: CreditCard },
            { title: "Shipping", href: "/settings/shipping", icon: Truck },
            { title: "Taxes", href: "/settings/taxes", icon: Settings },
        ]
    },
];

export function DashboardSidebar({ open, onOpenChange }: DashboardSidebarProps) {
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const toggleExpanded = (title: string) => {
        setExpandedItems(prev =>
            prev.includes(title)
                ? prev.filter(item => item !== title)
                : [...prev, title]
        );
    };

    const renderNavItem = (item: NavItem, level = 0) => {
        const isExpanded = expandedItems.includes(item.title);
        const hasChildren = item.children && item.children.length > 0;

        return (
            <div key={item.title} className="space-y-1">
                <div className="flex items-center">
                    <Link
                        href={item.href}
                        className={cn(
                            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors",
                            "w-full text-left",
                            level > 0 && "ml-6 text-muted-foreground"
                        )}
                    >
                        <item.icon className="h-4 w-4" />
                        <span className="flex-1">{item.title}</span>
                        {item.badge && (
                            <Badge variant="secondary" className="ml-auto">
                                {item.badge}
                            </Badge>
                        )}
                    </Link>
                    {hasChildren && (
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 ml-2"
                            onClick={() => toggleExpanded(item.title)}
                        >
                            {isExpanded ? (
                                <ChevronDown className="h-4 w-4" />
                            ) : (
                                <ChevronRight className="h-4 w-4" />
                            )}
                        </Button>
                    )}
                </div>
                {hasChildren && isExpanded && (
                    <div className="space-y-1">
                        {item.children?.map(child => renderNavItem(child, level + 1))}
                    </div>
                )}
            </div>
        );
    };

    return (
        <>
            <div className={cn(
                "fixed inset-y-0 left-0 z-50 w-64 bg-background border-r border-border transition-transform duration-300 ease-in-out",
                open ? "translate-x-0" : "-translate-x-full",
                "lg:translate-x-0"
            )}>
                <div className="flex h-full flex-col">
                    {/* Header */}
                    <div className="flex items-center justify-between p-6 border-b border-border ">
                        <Link href="/" className="flex items-center gap-2 dark:bg-white dark:rounded-2xl dark:p-2 dark:w-full">
                            <Image
                                src="/logo.png"
                                alt="Centre Research"
                                width={150}
                                height={50}
                                className="rounded-lg"
                            />
                            {/* <div className="flex flex-col">
                <span className="font-semibold text-sm">Centre Research</span>
                <span className="text-xs text-muted-foreground">Peptide Store</span>
              </div> */}
                        </Link>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="lg:hidden"
                            onClick={() => onOpenChange(false)}
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>

                    {/* Navigation */}
                    <ScrollArea className="flex-1 px-4 py-6">
                        <nav className="space-y-2">
                            {navItems.map(item => renderNavItem(item))}
                        </nav>
                    </ScrollArea>

                    {/* Footer */}
                    <div className="p-4 border-t border-border">
                        <div className="flex items-center gap-3 text-sm text-muted-foreground">
                            <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                            <span>System Status: Online</span>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
