const express = require('express');
const { body, param, query } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const validateRequest = require('../middleware/validateRequest');
const { asyncHandler } = require('../middleware/errorHandler');
const { requireRole, requirePermission } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get all shipments
router.get('/', requirePermission('SHIPPING', 'READ'), [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('orderId').optional().isString().withMessage('Order ID must be a string'),
  query('status').optional().isString().withMessage('Status must be a string'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, orderId, status } = req.query;
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Build filter conditions
  const where = {};
  if (orderId) where.orderId = orderId;
  if (status) where.status = status;

  // Get shipments and total count
  const [shipments, total] = await Promise.all([
    prisma.shipment.findMany({
      where,
      skip,
      take: parseInt(limit),
      orderBy: { createdAt: 'desc' },
      include: {
        order: {
          select: {
            orderNumber: true,
            customer: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        }
      }
    }),
    prisma.shipment.count({ where })
  ]);

  res.json({
    success: true,
    data: {
      shipments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    }
  });
}));

// Create shipment
router.post('/', requirePermission('SHIPPING', 'CREATE'), [
  body('orderId').isString().withMessage('Order ID is required'),
  body('carrier').notEmpty().withMessage('Carrier is required'),
  body('trackingNumber').optional().isString().withMessage('Tracking number must be a string'),
  body('trackingUrl').optional().isURL().withMessage('Tracking URL must be valid'),
  body('status').optional().isIn(['PENDING', 'SHIPPED', 'IN_TRANSIT', 'DELIVERED', 'RETURNED', 'CANCELLED']).withMessage('Invalid status'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { orderId, carrier, trackingNumber, trackingUrl, status = 'PENDING' } = req.body;

  // Verify order exists
  const order = await prisma.order.findUnique({
    where: { id: orderId },
    select: { id: true, orderNumber: true, status: true }
  });

  if (!order) {
    return res.status(404).json({
      success: false,
      error: 'Order not found'
    });
  }

  // Create shipment
  const shipment = await prisma.shipment.create({
    data: {
      orderId,
      carrier,
      trackingNumber,
      trackingUrl,
      status,
      shippedAt: status === 'SHIPPED' ? new Date() : null
    },
    include: {
      order: {
        select: {
          orderNumber: true,
          customer: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      }
    }
  });

  // Update order status if shipment is shipped
  if (status === 'SHIPPED' && order.status === 'PROCESSING') {
    await prisma.order.update({
      where: { id: orderId },
      data: { status: 'SHIPPED' }
    });
  }

  res.status(201).json({
    success: true,
    data: shipment
  });
}));

// Update shipment tracking
router.put('/:id/tracking', requirePermission('SHIPPING', 'UPDATE'), [
  param('id').isString().withMessage('Shipment ID is required'),
  body('trackingNumber').optional().isString().withMessage('Tracking number must be a string'),
  body('trackingUrl').optional().isURL().withMessage('Tracking URL must be valid'),
  body('status').optional().isIn(['PENDING', 'SHIPPED', 'IN_TRANSIT', 'DELIVERED', 'RETURNED', 'CANCELLED']).withMessage('Invalid status'),
  body('carrier').optional().isString().withMessage('Carrier must be a string'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { trackingNumber, trackingUrl, status, carrier } = req.body;

  // Verify shipment exists
  const existingShipment = await prisma.shipment.findUnique({
    where: { id },
    include: {
      order: {
        select: { id: true, status: true }
      }
    }
  });

  if (!existingShipment) {
    return res.status(404).json({
      success: false,
      error: 'Shipment not found'
    });
  }

  // Prepare update data
  const updateData = {};
  if (trackingNumber !== undefined) updateData.trackingNumber = trackingNumber;
  if (trackingUrl !== undefined) updateData.trackingUrl = trackingUrl;
  if (carrier !== undefined) updateData.carrier = carrier;
  if (status !== undefined) {
    updateData.status = status;

    // Set timestamps based on status
    if (status === 'SHIPPED' && !existingShipment.shippedAt) {
      updateData.shippedAt = new Date();
    }
    if (status === 'DELIVERED') {
      updateData.deliveredAt = new Date();
    }
  }

  // Update shipment
  const shipment = await prisma.shipment.update({
    where: { id },
    data: updateData,
    include: {
      order: {
        select: {
          orderNumber: true,
          customer: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      }
    }
  });

  // Update order status based on shipment status
  if (status) {
    let orderStatus = null;
    if (status === 'SHIPPED' && existingShipment.order.status === 'PROCESSING') {
      orderStatus = 'SHIPPED';
    } else if (status === 'DELIVERED' && ['PROCESSING', 'SHIPPED'].includes(existingShipment.order.status)) {
      orderStatus = 'DELIVERED';
    }

    if (orderStatus) {
      await prisma.order.update({
        where: { id: existingShipment.order.id },
        data: { status: orderStatus }
      });
    }
  }

  res.json({
    success: true,
    data: shipment
  });
}));

// Get single shipment
router.get('/:id', requirePermission('SHIPPING', 'READ'), [
  param('id').isString().withMessage('Shipment ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  const shipment = await prisma.shipment.findUnique({
    where: { id },
    include: {
      order: {
        select: {
          orderNumber: true,
          status: true,
          customer: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          },
          shippingAddress: true
        }
      }
    }
  });

  if (!shipment) {
    return res.status(404).json({
      success: false,
      error: 'Shipment not found'
    });
  }

  res.json({
    success: true,
    data: shipment
  });
}));

// Delete shipment
router.delete('/:id', requirePermission('SHIPPING', 'DELETE'), [
  param('id').isString().withMessage('Shipment ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Verify shipment exists
  const existingShipment = await prisma.shipment.findUnique({
    where: { id },
    select: { id: true, status: true }
  });

  if (!existingShipment) {
    return res.status(404).json({
      success: false,
      error: 'Shipment not found'
    });
  }

  // Only allow deletion if shipment is not delivered
  if (existingShipment.status === 'DELIVERED') {
    return res.status(400).json({
      success: false,
      error: 'Cannot delete delivered shipment'
    });
  }

  await prisma.shipment.delete({
    where: { id }
  });

  res.json({
    success: true,
    message: 'Shipment deleted successfully'
  });
}));

// Get shipments by order ID
router.get('/order/:orderId', requirePermission('SHIPPING', 'READ'), [
  param('orderId').isString().withMessage('Order ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { orderId } = req.params;

  // Verify order exists
  const order = await prisma.order.findUnique({
    where: { id: orderId },
    select: { id: true }
  });

  if (!order) {
    return res.status(404).json({
      success: false,
      error: 'Order not found'
    });
  }

  const shipments = await prisma.shipment.findMany({
    where: { orderId },
    orderBy: { createdAt: 'desc' },
    include: {
      order: {
        select: {
          orderNumber: true
        }
      }
    }
  });

  res.json({
    success: true,
    data: shipments
  });
}));

// Get shipping zones
router.get('/zones', requirePermission('SHIPPING', 'READ'), asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'Shipping zones endpoint - To be implemented',
    data: []
  });
}));

// Get shipping rates
router.get('/rates', requirePermission('SHIPPING', 'READ'), [
  query('zoneId').optional().isString().withMessage('Zone ID must be a string'),
  validateRequest
], asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'Shipping rates endpoint - To be implemented',
    data: []
  });
}));

module.exports = router;