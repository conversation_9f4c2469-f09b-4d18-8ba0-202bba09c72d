{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction ScrollArea({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\r\n  return (\r\n    <ScrollAreaPrimitive.Root\r\n      data-slot=\"scroll-area\"\r\n      className={cn(\"relative\", className)}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.Viewport\r\n        data-slot=\"scroll-area-viewport\"\r\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\r\n      >\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar />\r\n      <ScrollAreaPrimitive.Corner />\r\n    </ScrollAreaPrimitive.Root>\r\n  )\r\n}\r\n\r\nfunction ScrollBar({\r\n  className,\r\n  orientation = \"vertical\",\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\r\n  return (\r\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n      data-slot=\"scroll-area-scrollbar\"\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"flex touch-none p-px transition-colors select-none\",\r\n        orientation === \"vertical\" &&\r\n          \"h-full w-2.5 border-l border-l-transparent\",\r\n        orientation === \"horizontal\" &&\r\n          \"h-2.5 flex-col border-t border-t-transparent\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.ScrollAreaThumb\r\n        data-slot=\"scroll-area-thumb\"\r\n        className=\"bg-border relative flex-1 rounded-full\"\r\n      />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n  )\r\n}\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/dashboard/dashboard-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport {\r\n    LayoutDashboard,\r\n    ShoppingCart,\r\n    Package,\r\n    Users,\r\n    BarChart3,\r\n    Settings,\r\n    Gift,\r\n    Truck,\r\n    CreditCard,\r\n    FileText,\r\n    Bell,\r\n    ChevronDown,\r\n    ChevronRight,\r\n    Home,\r\n    Tag,\r\n    Warehouse,\r\n    MessageSquare,\r\n    Mail,\r\n    X\r\n} from \"lucide-react\";\r\n\r\ninterface DashboardSidebarProps {\r\n    open: boolean;\r\n    onOpenChange: (open: boolean) => void;\r\n}\r\n\r\ninterface NavItem {\r\n    title: string;\r\n    href: string;\r\n    icon: React.ComponentType<any>;\r\n    badge?: string;\r\n    children?: NavItem[];\r\n}\r\n\r\nconst navItems: NavItem[] = [\r\n    {\r\n        title: \"Dashboard\",\r\n        href: \"/\",\r\n        icon: LayoutDashboard,\r\n    },\r\n    {\r\n        title: \"Orders\",\r\n        href: \"/orders\",\r\n        icon: ShoppingCart,\r\n        badge: \"23\",\r\n        children: [\r\n            { title: \"All Orders\", href: \"/orders\", icon: ShoppingCart },\r\n            { title: \"Pending\", href: \"/orders/pending\", icon: ShoppingCart },\r\n            { title: \"Processing\", href: \"/orders/processing\", icon: ShoppingCart },\r\n            { title: \"Shipped\", href: \"/orders/shipped\", icon: ShoppingCart },\r\n            { title: \"Delivered\", href: \"/orders/delivered\", icon: ShoppingCart },\r\n            { title: \"Cancelled\", href: \"/orders/cancelled\", icon: ShoppingCart },\r\n        ]\r\n    },\r\n    {\r\n        title: \"Products\",\r\n        href: \"/products\",\r\n        icon: Package,\r\n        children: [\r\n            { title: \"All Products\", href: \"/products\", icon: Package },\r\n            { title: \"Categories\", href: \"/products/categories\", icon: Tag },\r\n            { title: \"Collections\", href: \"/products/collections\", icon: Package },\r\n            { title: \"Inventory\", href: \"/products/inventory\", icon: Warehouse },\r\n        ]\r\n    },\r\n    {\r\n        title: \"Customers\",\r\n        href: \"/customers\",\r\n        icon: Users,\r\n        children: [\r\n            { title: \"All Customers\", href: \"/customers\", icon: Users },\r\n            { title: \"B2C Customers\", href: \"/customers/b2c\", icon: Users },\r\n            { title: \"B2B Customers\", href: \"/customers/b2b\", icon: Users },\r\n            { title: \"Enterprise\", href: \"/customers/enterprise\", icon: Users },\r\n        ]\r\n    },\r\n    {\r\n        title: \"Analytics\",\r\n        href: \"/analytics\",\r\n        icon: BarChart3,\r\n        children: [\r\n            { title: \"Overview\", href: \"/analytics\", icon: BarChart3 },\r\n            { title: \"Sales Reports\", href: \"/analytics/sales\", icon: BarChart3 },\r\n            { title: \"Product Performance\", href: \"/analytics/products\", icon: Package },\r\n            { title: \"Customer Insights\", href: \"/analytics/customers\", icon: Users },\r\n        ]\r\n    },\r\n    {\r\n        title: \"Marketing\",\r\n        href: \"/marketing\",\r\n        icon: MessageSquare,\r\n        children: [\r\n            { title: \"Campaigns\", href: \"/marketing/campaigns\", icon: MessageSquare },\r\n            { title: \"Email Marketing\", href: \"/marketing/email\", icon: Mail },\r\n            { title: \"Promotions\", href: \"/marketing/promotions\", icon: Gift },\r\n            { title: \"Loyalty Program\", href: \"/marketing/loyalty\", icon: Gift },\r\n        ]\r\n    },\r\n    {\r\n        title: \"Promotions\",\r\n        href: \"/coupons\",\r\n        icon: Tag,\r\n    },\r\n    {\r\n        title: \"Payments\",\r\n        href: \"/payments\",\r\n        icon: CreditCard,\r\n    },\r\n    {\r\n        title: \"Shipping\",\r\n        href: \"/shipping\",\r\n        icon: Truck,\r\n    },\r\n    {\r\n        title: \"Content\",\r\n        href: \"/content\",\r\n        icon: FileText,\r\n        children: [\r\n            { title: \"Pages\", href: \"/content/pages\", icon: FileText },\r\n            { title: \"Blog\", href: \"/content/blog\", icon: FileText },\r\n            { title: \"Navigation\", href: \"/content/navigation\", icon: FileText },\r\n            { title: \"Media\", href: \"/content/media\", icon: FileText },\r\n        ]\r\n    },\r\n    {\r\n        title: \"Users\",\r\n        href: \"/users\",\r\n        icon: Users,\r\n    },\r\n    {\r\n        title: \"Settings\",\r\n        href: \"/settings\",\r\n        icon: Settings,\r\n        children: [\r\n            { title: \"General\", href: \"/settings/general\", icon: Settings },\r\n            { title: \"Payments\", href: \"/settings/payments\", icon: CreditCard },\r\n            { title: \"Shipping\", href: \"/settings/shipping\", icon: Truck },\r\n            { title: \"Taxes\", href: \"/settings/taxes\", icon: Settings },\r\n        ]\r\n    },\r\n];\r\n\r\nexport function DashboardSidebar({ open, onOpenChange }: DashboardSidebarProps) {\r\n    const [expandedItems, setExpandedItems] = useState<string[]>([]);\r\n\r\n    const toggleExpanded = (title: string) => {\r\n        setExpandedItems(prev =>\r\n            prev.includes(title)\r\n                ? prev.filter(item => item !== title)\r\n                : [...prev, title]\r\n        );\r\n    };\r\n\r\n    const renderNavItem = (item: NavItem, level = 0) => {\r\n        const isExpanded = expandedItems.includes(item.title);\r\n        const hasChildren = item.children && item.children.length > 0;\r\n\r\n        return (\r\n            <div key={item.title} className=\"space-y-1\">\r\n                <div className=\"flex items-center\">\r\n                    <Link\r\n                        href={item.href}\r\n                        className={cn(\r\n                            \"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors\",\r\n                            \"w-full text-left\",\r\n                            level > 0 && \"ml-6 text-muted-foreground\"\r\n                        )}\r\n                    >\r\n                        <item.icon className=\"h-4 w-4\" />\r\n                        <span className=\"flex-1\">{item.title}</span>\r\n                        {item.badge && (\r\n                            <Badge variant=\"secondary\" className=\"ml-auto\">\r\n                                {item.badge}\r\n                            </Badge>\r\n                        )}\r\n                    </Link>\r\n                    {hasChildren && (\r\n                        <Button\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            className=\"h-8 w-8 p-0 ml-2\"\r\n                            onClick={() => toggleExpanded(item.title)}\r\n                        >\r\n                            {isExpanded ? (\r\n                                <ChevronDown className=\"h-4 w-4\" />\r\n                            ) : (\r\n                                <ChevronRight className=\"h-4 w-4\" />\r\n                            )}\r\n                        </Button>\r\n                    )}\r\n                </div>\r\n                {hasChildren && isExpanded && (\r\n                    <div className=\"space-y-1\">\r\n                        {item.children?.map(child => renderNavItem(child, level + 1))}\r\n                    </div>\r\n                )}\r\n            </div>\r\n        );\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <div className={cn(\r\n                \"fixed inset-y-0 left-0 z-50 w-64 bg-background border-r border-border transition-transform duration-300 ease-in-out\",\r\n                open ? \"translate-x-0\" : \"-translate-x-full\",\r\n                \"lg:translate-x-0\"\r\n            )}>\r\n                <div className=\"flex h-full flex-col\">\r\n                    {/* Header */}\r\n                    <div className=\"flex items-center justify-between p-6 border-b border-border \">\r\n                        <Link href=\"/\" className=\"flex items-center gap-2 dark:bg-white dark:rounded-2xl dark:p-2 dark:w-full\">\r\n                            <Image\r\n                                src=\"/logo.png\"\r\n                                alt=\"Centre Research\"\r\n                                width={150}\r\n                                height={50}\r\n                                className=\"rounded-lg\"\r\n                            />\r\n                            {/* <div className=\"flex flex-col\">\r\n                <span className=\"font-semibold text-sm\">Centre Research</span>\r\n                <span className=\"text-xs text-muted-foreground\">Peptide Store</span>\r\n              </div> */}\r\n                        </Link>\r\n                        <Button\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            className=\"lg:hidden\"\r\n                            onClick={() => onOpenChange(false)}\r\n                        >\r\n                            <X className=\"h-4 w-4\" />\r\n                        </Button>\r\n                    </div>\r\n\r\n                    {/* Navigation */}\r\n                    <ScrollArea className=\"flex-1 px-4 py-6\">\r\n                        <nav className=\"space-y-2\">\r\n                            {navItems.map(item => renderNavItem(item))}\r\n                        </nav>\r\n                    </ScrollArea>\r\n\r\n                    {/* Footer */}\r\n                    <div className=\"p-4 border-t border-border\">\r\n                        <div className=\"flex items-center gap-3 text-sm text-muted-foreground\">\r\n                            <div className=\"h-2 w-2 bg-green-500 rounded-full\"></div>\r\n                            <span>System Status: Online</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AA4CA,MAAM,WAAsB;IACxB;QACI,OAAO;QACP,MAAM;QACN,MAAM,4NAAA,CAAA,kBAAe;IACzB;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,sNAAA,CAAA,eAAY;QAClB,OAAO;QACP,UAAU;YACN;gBAAE,OAAO;gBAAc,MAAM;gBAAW,MAAM,sNAAA,CAAA,eAAY;YAAC;YAC3D;gBAAE,OAAO;gBAAW,MAAM;gBAAmB,MAAM,sNAAA,CAAA,eAAY;YAAC;YAChE;gBAAE,OAAO;gBAAc,MAAM;gBAAsB,MAAM,sNAAA,CAAA,eAAY;YAAC;YACtE;gBAAE,OAAO;gBAAW,MAAM;gBAAmB,MAAM,sNAAA,CAAA,eAAY;YAAC;YAChE;gBAAE,OAAO;gBAAa,MAAM;gBAAqB,MAAM,sNAAA,CAAA,eAAY;YAAC;YACpE;gBAAE,OAAO;gBAAa,MAAM;gBAAqB,MAAM,sNAAA,CAAA,eAAY;YAAC;SACvE;IACL;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,UAAU;YACN;gBAAE,OAAO;gBAAgB,MAAM;gBAAa,MAAM,wMAAA,CAAA,UAAO;YAAC;YAC1D;gBAAE,OAAO;gBAAc,MAAM;gBAAwB,MAAM,gMAAA,CAAA,MAAG;YAAC;YAC/D;gBAAE,OAAO;gBAAe,MAAM;gBAAyB,MAAM,wMAAA,CAAA,UAAO;YAAC;YACrE;gBAAE,OAAO;gBAAa,MAAM;gBAAuB,MAAM,4MAAA,CAAA,YAAS;YAAC;SACtE;IACL;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,UAAU;YACN;gBAAE,OAAO;gBAAiB,MAAM;gBAAc,MAAM,oMAAA,CAAA,QAAK;YAAC;YAC1D;gBAAE,OAAO;gBAAiB,MAAM;gBAAkB,MAAM,oMAAA,CAAA,QAAK;YAAC;YAC9D;gBAAE,OAAO;gBAAiB,MAAM;gBAAkB,MAAM,oMAAA,CAAA,QAAK;YAAC;YAC9D;gBAAE,OAAO;gBAAc,MAAM;gBAAyB,MAAM,oMAAA,CAAA,QAAK;YAAC;SACrE;IACL;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,kNAAA,CAAA,YAAS;QACf,UAAU;YACN;gBAAE,OAAO;gBAAY,MAAM;gBAAc,MAAM,kNAAA,CAAA,YAAS;YAAC;YACzD;gBAAE,OAAO;gBAAiB,MAAM;gBAAoB,MAAM,kNAAA,CAAA,YAAS;YAAC;YACpE;gBAAE,OAAO;gBAAuB,MAAM;gBAAuB,MAAM,wMAAA,CAAA,UAAO;YAAC;YAC3E;gBAAE,OAAO;gBAAqB,MAAM;gBAAwB,MAAM,oMAAA,CAAA,QAAK;YAAC;SAC3E;IACL;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,wNAAA,CAAA,gBAAa;QACnB,UAAU;YACN;gBAAE,OAAO;gBAAa,MAAM;gBAAwB,MAAM,wNAAA,CAAA,gBAAa;YAAC;YACxE;gBAAE,OAAO;gBAAmB,MAAM;gBAAoB,MAAM,kMAAA,CAAA,OAAI;YAAC;YACjE;gBAAE,OAAO;gBAAc,MAAM;gBAAyB,MAAM,kMAAA,CAAA,OAAI;YAAC;YACjE;gBAAE,OAAO;gBAAmB,MAAM;gBAAsB,MAAM,kMAAA,CAAA,OAAI;YAAC;SACtE;IACL;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;IACb;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,kNAAA,CAAA,aAAU;IACpB;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACf;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,8MAAA,CAAA,WAAQ;QACd,UAAU;YACN;gBAAE,OAAO;gBAAS,MAAM;gBAAkB,MAAM,8MAAA,CAAA,WAAQ;YAAC;YACzD;gBAAE,OAAO;gBAAQ,MAAM;gBAAiB,MAAM,8MAAA,CAAA,WAAQ;YAAC;YACvD;gBAAE,OAAO;gBAAc,MAAM;gBAAuB,MAAM,8MAAA,CAAA,WAAQ;YAAC;YACnE;gBAAE,OAAO;gBAAS,MAAM;gBAAkB,MAAM,8MAAA,CAAA,WAAQ;YAAC;SAC5D;IACL;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACf;IACA;QACI,OAAO;QACP,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,UAAU;YACN;gBAAE,OAAO;gBAAW,MAAM;gBAAqB,MAAM,0MAAA,CAAA,WAAQ;YAAC;YAC9D;gBAAE,OAAO;gBAAY,MAAM;gBAAsB,MAAM,kNAAA,CAAA,aAAU;YAAC;YAClE;gBAAE,OAAO;gBAAY,MAAM;gBAAsB,MAAM,oMAAA,CAAA,QAAK;YAAC;YAC7D;gBAAE,OAAO;gBAAS,MAAM;gBAAmB,MAAM,0MAAA,CAAA,WAAQ;YAAC;SAC7D;IACL;CACH;AAEM,SAAS,iBAAiB,EAAE,IAAI,EAAE,YAAY,EAAyB;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,iBAAiB,CAAC;QACpB,iBAAiB,CAAA,OACb,KAAK,QAAQ,CAAC,SACR,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,SAC7B;mBAAI;gBAAM;aAAM;IAE9B;IAEA,MAAM,gBAAgB,CAAC,MAAe,QAAQ,CAAC;QAC3C,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,KAAK;QACpD,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAE5D,qBACI,8OAAC;YAAqB,WAAU;;8BAC5B,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,4JAAA,CAAA,UAAI;4BACD,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,mIACA,oBACA,QAAQ,KAAK;;8CAGjB,8OAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAAU,KAAK,KAAK;;;;;;gCACnC,KAAK,KAAK,kBACP,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAChC,KAAK,KAAK;;;;;;;;;;;;wBAItB,6BACG,8OAAC,kIAAA,CAAA,SAAM;4BACH,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe,KAAK,KAAK;sCAEvC,2BACG,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAKvC,eAAe,4BACZ,8OAAC;oBAAI,WAAU;8BACV,KAAK,QAAQ,EAAE,IAAI,CAAA,QAAS,cAAc,OAAO,QAAQ;;;;;;;WAnC5D,KAAK,KAAK;;;;;IAwC5B;IAEA,qBACI;kBACI,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,uHACA,OAAO,kBAAkB,qBACzB;sBAEA,cAAA,8OAAC;gBAAI,WAAU;;kCAEX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACrB,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACF,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAOlB,8OAAC,kIAAA,CAAA,SAAM;gCACH,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,aAAa;0CAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKrB,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCAClB,cAAA,8OAAC;4BAAI,WAAU;sCACV,SAAS,GAAG,CAAC,CAAA,OAAQ,cAAc;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/dashboard/dashboard-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n    DropdownMenu,\r\n    DropdownMenuContent,\r\n    DropdownMenuItem,\r\n    DropdownMenuLabel,\r\n    DropdownMenuSeparator,\r\n    DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport {\r\n    Menu,\r\n    Search,\r\n    Bell,\r\n    Plus,\r\n    Sun,\r\n    Moon,\r\n    User,\r\n    Settings,\r\n    LogOut,\r\n    CreditCard,\r\n    Users,\r\n    HelpCircle\r\n} from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useAuth } from \"@/contexts/auth-context\";\r\n\r\ninterface DashboardHeaderProps {\r\n    onMenuClick: () => void;\r\n}\r\n\r\nexport function DashboardHeader({ onMenuClick }: DashboardHeaderProps) {\r\n    const { setTheme, theme } = useTheme();\r\n    const { logout } = useAuth();\r\n\r\n    return (\r\n        <header className=\"sticky top-0 z-40 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n            <div className=\"flex h-16 items-center justify-between px-6\">\r\n                {/* Left side */}\r\n                <div className=\"flex items-center gap-4\">\r\n                    <Button\r\n                        variant=\"ghost\"\r\n                        size=\"sm\"\r\n                        className=\"lg:hidden\"\r\n                        onClick={onMenuClick}\r\n                    >\r\n                        <Menu className=\"h-5 w-5\" />\r\n                    </Button>\r\n\r\n                    {/* Search */}\r\n                    <div className=\"relative hidden md:block\">\r\n                        <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n                        <Input\r\n                            type=\"search\"\r\n                            placeholder=\"Search orders, products, customers...\"\r\n                            className=\"w-64 pl-10\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Right side */}\r\n                <div className=\"flex items-center gap-2\">\r\n                    {/* Quick Actions */}\r\n                    <DropdownMenu>\r\n                        <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"outline\" size=\"sm\">\r\n                                <Plus className=\"h-4 w-4 mr-2\" />\r\n                                Create\r\n                            </Button>\r\n                        </DropdownMenuTrigger>\r\n                        <DropdownMenuContent align=\"end\" className=\"w-48\">\r\n                            <DropdownMenuItem>\r\n                                <Plus className=\"h-4 w-4 mr-2\" />\r\n                                Add Product\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem>\r\n                                <Users className=\"h-4 w-4 mr-2\" />\r\n                                Add Customer\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem>\r\n                                <CreditCard className=\"h-4 w-4 mr-2\" />\r\n                                Manual Order\r\n                            </DropdownMenuItem>\r\n                        </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n\r\n                    {/* Theme Toggle */}\r\n                    <Button\r\n                        variant=\"ghost\"\r\n                        size=\"sm\"\r\n                        onClick={() => setTheme(theme === \"dark\" ? \"light\" : \"dark\")}\r\n                    >\r\n                        <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n                        <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n                        <span className=\"sr-only\">Toggle theme</span>\r\n                    </Button>\r\n\r\n                    {/* Notifications */}\r\n                    <DropdownMenu>\r\n                        <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\r\n                                <Bell className=\"h-4 w-4\" />\r\n                                <Badge\r\n                                    variant=\"destructive\"\r\n                                    className=\"absolute -top-1 -right-1 h-5 w-5 p-0 text-xs\"\r\n                                >\r\n                                    3\r\n                                </Badge>\r\n                            </Button>\r\n                        </DropdownMenuTrigger>\r\n                        <DropdownMenuContent align=\"end\" className=\"w-80\">\r\n                            <DropdownMenuLabel>Notifications</DropdownMenuLabel>\r\n                            <DropdownMenuSeparator />\r\n                            <div className=\"space-y-2 p-2\">\r\n                                <div className=\"flex items-start gap-3 p-2 rounded-lg hover:bg-accent\">\r\n                                    <div className=\"h-2 w-2 bg-blue-500 rounded-full mt-2\"></div>\r\n                                    <div className=\"flex-1\">\r\n                                        <p className=\"text-sm font-medium\">New order received</p>\r\n                                        <p className=\"text-xs text-muted-foreground\">Order #1234 from John Doe</p>\r\n                                        <p className=\"text-xs text-muted-foreground\">2 minutes ago</p>\r\n                                    </div>\r\n                                </div>\r\n                                <div className=\"flex items-start gap-3 p-2 rounded-lg hover:bg-accent\">\r\n                                    <div className=\"h-2 w-2 bg-yellow-500 rounded-full mt-2\"></div>\r\n                                    <div className=\"flex-1\">\r\n                                        <p className=\"text-sm font-medium\">Low stock alert</p>\r\n                                        <p className=\"text-xs text-muted-foreground\">Peptide XYZ has only 5 units left</p>\r\n                                        <p className=\"text-xs text-muted-foreground\">1 hour ago</p>\r\n                                    </div>\r\n                                </div>\r\n                                <div className=\"flex items-start gap-3 p-2 rounded-lg hover:bg-accent\">\r\n                                    <div className=\"h-2 w-2 bg-red-500 rounded-full mt-2\"></div>\r\n                                    <div className=\"flex-1\">\r\n                                        <p className=\"text-sm font-medium\">Batch expiring soon</p>\r\n                                        <p className=\"text-xs text-muted-foreground\">Batch #ABC123 expires in 3 days</p>\r\n                                        <p className=\"text-xs text-muted-foreground\">30 minutes ago</p>\r\n                                    </div>\r\n                                </div>\r\n                                <div className=\"flex items-start gap-3 p-2 rounded-lg hover:bg-accent\">\r\n                                    <div className=\"h-2 w-2 bg-green-500 rounded-full mt-2\"></div>\r\n                                    <div className=\"flex-1\">\r\n                                        <p className=\"text-sm font-medium\">Payment received</p>\r\n                                        <p className=\"text-xs text-muted-foreground\">$2,450.00 for order #1233</p>\r\n                                        <p className=\"text-xs text-muted-foreground\">3 hours ago</p>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <DropdownMenuSeparator />\r\n                            <DropdownMenuItem className=\"justify-center\">\r\n                                View all notifications\r\n                            </DropdownMenuItem>\r\n                        </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n\r\n                    {/* Profile Menu */}\r\n                    <DropdownMenu>\r\n                        <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\r\n                                <Avatar className=\"h-8 w-8\">\r\n                                    <AvatarImage src=\"/avatars/01.png\" alt=\"Admin\" />\r\n                                    <AvatarFallback>AD</AvatarFallback>\r\n                                </Avatar>\r\n                            </Button>\r\n                        </DropdownMenuTrigger>\r\n                        <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\r\n                            <DropdownMenuLabel className=\"font-normal\">\r\n                                <div className=\"flex flex-col space-y-1\">\r\n                                    <p className=\"text-sm font-medium leading-none\">Admin User</p>\r\n                                    <p className=\"text-xs leading-none text-muted-foreground\">\r\n                                        <EMAIL>\r\n                                    </p>\r\n                                </div>\r\n                            </DropdownMenuLabel>\r\n                            <DropdownMenuSeparator />\r\n                            <DropdownMenuItem>\r\n                                <User className=\"mr-2 h-4 w-4\" />\r\n                                <span>Profile</span>\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem>\r\n                                <Settings className=\"mr-2 h-4 w-4\" />\r\n                                <span>Settings</span>\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem>\r\n                                <HelpCircle className=\"mr-2 h-4 w-4\" />\r\n                                <span>Help & Support</span>\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuSeparator />\r\n                            <DropdownMenuItem\r\n                                onClick={() => {\r\n                                    logout();\r\n                                }}\r\n                            >\r\n                                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                                <span>Log out</span>\r\n                            </DropdownMenuItem>\r\n                        </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                </div>\r\n            </div>\r\n        </header>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AA7BA;;;;;;;;;;AAmCO,SAAS,gBAAgB,EAAE,WAAW,EAAwB;IACjE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEzB,qBACI,8OAAC;QAAO,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BACH,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIpB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACF,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;;;8BAMtB,8OAAC;oBAAI,WAAU;;sCAEX,8OAAC,4IAAA,CAAA,eAAY;;8CACT,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CACxB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC3B,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIzC,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACvC,8OAAC,4IAAA,CAAA,mBAAgB;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,8OAAC,4IAAA,CAAA,mBAAgB;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGtC,8OAAC,4IAAA,CAAA,mBAAgB;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOnD,8OAAC,kIAAA,CAAA,SAAM;4BACH,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;;8CAErD,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI9B,8OAAC,4IAAA,CAAA,eAAY;;8CACT,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CACxB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DACxC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC,iIAAA,CAAA,QAAK;gDACF,SAAQ;gDACR,WAAU;0DACb;;;;;;;;;;;;;;;;;8CAKT,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACvC,8OAAC,4IAAA,CAAA,oBAAiB;sDAAC;;;;;;sDACnB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAC7C,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAC7C,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAC7C,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAC7C,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;;;;;;;sDAIzD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;sCAOrD,8OAAC,4IAAA,CAAA,eAAY;;8CACT,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CACxB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAC9B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DACd,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAI;oDAAkB,KAAI;;;;;;8DACvC,8OAAC,kIAAA,CAAA,iBAAc;8DAAC;;;;;;;;;;;;;;;;;;;;;;8CAI5B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;sDACxD,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDACzB,cAAA,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,8OAAC;wDAAE,WAAU;kEAA6C;;;;;;;;;;;;;;;;;sDAKlE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAEV,8OAAC,4IAAA,CAAA,mBAAgB;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAEV,8OAAC,4IAAA,CAAA,mBAAgB;;8DACb,8OAAC,8NAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;8DAAK;;;;;;;;;;;;sDAEV,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CACb,SAAS;gDACL;4CACJ;;8DAEA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}, {"offset": {"line": 1727, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/dashboard/dashboard-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { DashboardSidebar } from \"./dashboard-sidebar\";\r\nimport { DashboardHeader } from \"./dashboard-header\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface DashboardLayoutProps {\r\n    children: React.ReactNode;\r\n}\r\n\r\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\r\n    const [sidebarOpen, setSidebarOpen] = useState(false);\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-background\">\r\n            {/* Sidebar */}\r\n            <DashboardSidebar open={sidebarOpen} onOpenChange={setSidebarOpen} />\r\n\r\n            {/* Main content */}\r\n            <div className={cn(\r\n                \"transition-all duration-300 ease-in-out\",\r\n                \"lg:pl-64\" // Always show sidebar on large screens\r\n            )}>\r\n                {/* Header */}\r\n                <DashboardHeader onMenuClick={() => setSidebarOpen(true)} />\r\n\r\n                {/* Page content */}\r\n                <main className=\"p-6\">\r\n                    {children}\r\n                </main>\r\n            </div>\r\n\r\n            {/* Mobile sidebar overlay */}\r\n            {sidebarOpen && (\r\n                <div\r\n                    className=\"fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden\"\r\n                    onClick={() => setSidebarOpen(false)}\r\n                />\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACI,8OAAC;QAAI,WAAU;;0BAEX,8OAAC,uJAAA,CAAA,mBAAgB;gBAAC,MAAM;gBAAa,cAAc;;;;;;0BAGnD,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,2CACA,WAAW,uCAAuC;;;kCAGlD,8OAAC,sJAAA,CAAA,kBAAe;wBAAC,aAAa,IAAM,eAAe;;;;;;kCAGnD,8OAAC;wBAAK,WAAU;kCACX;;;;;;;;;;;;YAKR,6BACG,8OAAC;gBACG,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;;;;;;;AAKlD", "debugId": null}}, {"offset": {"line": 1800, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1918, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2078, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2103, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/orders/orders-table.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n  AlertDialogTrigger,\r\n} from '@/components/ui/alert-dialog';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { \r\n  MoreHorizontal, \r\n  Edit, \r\n  Trash2, \r\n  Eye, \r\n  Package,\r\n  RefreshCw,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  Download,\r\n  Calendar\r\n} from 'lucide-react';\r\nimport { Order } from '@/lib/api';\r\nimport { format } from 'date-fns';\r\nimport { api } from '@/lib/api';\r\nimport { getToken } from '@/lib/api';\r\nimport { saveAs } from 'file-saver';\r\n\r\ninterface OrdersTableProps {\r\n  orders: Order[];\r\n  loading: boolean;\r\n  onEdit: (order: Order) => void;\r\n  onDelete: (orderId: string) => void;\r\n  onUpdateStatus: (order: Order) => void;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  onPageChange: (page: number) => void;\r\n}\r\n\r\nconst getStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case 'PENDING':\r\n      return 'bg-yellow-100 text-yellow-800 border-yellow-200';\r\n    case 'PROCESSING':\r\n      return 'bg-blue-100 text-blue-800 border-blue-200';\r\n    case 'SHIPPED':\r\n      return 'bg-purple-100 text-purple-800 border-purple-200';\r\n    case 'DELIVERED':\r\n      return 'bg-green-100 text-green-800 border-green-200';\r\n    case 'CANCELLED':\r\n      return 'bg-red-100 text-red-800 border-red-200';\r\n    case 'REFUNDED':\r\n      return 'bg-gray-100 text-gray-800 border-gray-200';\r\n    case 'ON_HOLD':\r\n      return 'bg-orange-100 text-orange-800 border-orange-200';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 border-gray-200';\r\n  }\r\n};\r\n\r\nconst getPaymentStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case 'PAID':\r\n      return 'bg-green-100 text-green-800 border-green-200';\r\n    case 'PENDING':\r\n      return 'bg-yellow-100 text-yellow-800 border-yellow-200';\r\n    case 'FAILED':\r\n      return 'bg-red-100 text-red-800 border-red-200';\r\n    case 'REFUNDED':\r\n      return 'bg-gray-100 text-gray-800 border-gray-200';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 border-gray-200';\r\n  }\r\n};\r\n\r\nfunction ordersToCSV(orders: Order[]): string {\r\n  const headers = ['Order ID', 'Customer', 'Status', 'Total', 'Date'];\r\n  const rows = orders.map(o => [\r\n    o.id,\r\n    o.customer,\r\n    o.status,\r\n    o.totalAmount?.toString() || '',\r\n    o.createdAt ? new Date(o.createdAt).toLocaleString() : ''\r\n  ]);\r\n  return [headers, ...rows].map(r => r.map(x => `\"${x}\"`).join(',')).join('\\n');\r\n}\r\n\r\nexport function OrdersTable({\r\n  orders,\r\n  loading,\r\n  onEdit,\r\n  onDelete,\r\n  onUpdateStatus,\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n}: OrdersTableProps) {\r\n  const [selected, setSelected] = useState<string[]>([]);\r\n  const [bulkAction, setBulkAction] = useState<null | 'delete' | 'export'>(null);\r\n  const [deletingId, setDeletingId] = useState<string | null>(null);\r\n\r\n  const handleDelete = (orderId: string) => {\r\n    setDeletingId(orderId);\r\n    onDelete(orderId);\r\n    setDeletingId(null);\r\n  };\r\n\r\n  const allSelected = orders.length > 0 && selected.length === orders.length;\r\n  const toggleSelectAll = () => {\r\n    if (allSelected) setSelected([]);\r\n    else setSelected(orders.map(o => o.id));\r\n  };\r\n  const toggleSelect = (id: string) => {\r\n    setSelected(sel => sel.includes(id) ? sel.filter(x => x !== id) : [...sel, id]);\r\n  };\r\n\r\n  const handleBulkDelete = async () => {\r\n    if (selected.length === 0) return;\r\n    if (!window.confirm(`Cancel ${selected.length} orders?`)) return;\r\n    // Call backend bulk delete endpoint\r\n    await fetch('/api/orders/bulk-delete', {\r\n      method: 'POST',\r\n      headers: { 'Content-Type': 'application/json' },\r\n      body: JSON.stringify({ ids: selected }),\r\n    });\r\n    setSelected([]);\r\n    setBulkAction(null);\r\n    // Optionally, refresh orders list here\r\n  };\r\n\r\n  const handleBulkExport = () => {\r\n    const selectedOrders = orders.filter(o => selected.includes(o.id));\r\n    const csv = ordersToCSV(selectedOrders);\r\n    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });\r\n    saveAs(blob, 'orders-export.csv');\r\n    setBulkAction(null);\r\n  };\r\n\r\n  const formatCurrency = (amount: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n    }).format(amount);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader>\r\n              <TableRow>\r\n                <TableHead>Order</TableHead>\r\n                <TableHead>Customer</TableHead>\r\n                <TableHead>Status</TableHead>\r\n                <TableHead>Payment</TableHead>\r\n                <TableHead>Total</TableHead>\r\n                <TableHead>Date</TableHead>\r\n                <TableHead>Actions</TableHead>\r\n              </TableRow>\r\n            </TableHeader>\r\n            <TableBody>\r\n              {[...Array(5)].map((_, i) => (\r\n                <TableRow key={i}>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-20\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n                      <Skeleton className=\"h-4 w-24\" />\r\n                    </div>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-6 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-6 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-20\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-8 w-8\" />\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (orders.length === 0) {\r\n    return (\r\n      <div className=\"rounded-md border border-dashed p-8 text-center\">\r\n        <Package className=\"mx-auto h-12 w-12 text-muted-foreground\" />\r\n        <h3 className=\"mt-2 text-sm font-medium text-muted-foreground\">No orders found</h3>\r\n        <p className=\"mt-1 text-sm text-muted-foreground\">\r\n          Get started by creating your first order.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead><input type=\"checkbox\" checked={allSelected} onChange={toggleSelectAll} /></TableHead>\r\n              <TableHead>Order</TableHead>\r\n              <TableHead>Customer</TableHead>\r\n              <TableHead>Status</TableHead>\r\n              <TableHead>Payment</TableHead>\r\n              <TableHead>Items</TableHead>\r\n              <TableHead>Total</TableHead>\r\n              <TableHead>Date</TableHead>\r\n              <TableHead className=\"w-[70px]\">Actions</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {orders.map((order) => (\r\n              <TableRow key={order.id}>\r\n                <TableCell><input type=\"checkbox\" checked={selected.includes(order.id)} onChange={() => toggleSelect(order.id)} /></TableCell>\r\n                <TableCell className=\"font-medium\">\r\n                  <div className=\"flex flex-col\">\r\n                    <span className=\"font-mono text-sm\">#{order.orderNumber}</span>\r\n                    {order.notes && Array.isArray(order.notes) && order.notes.length > 0 && (\r\n                      <span className=\"text-xs text-muted-foreground\">\r\n                        {order.notes[0].note.length > 30 \r\n                          ? `${order.notes[0].note.substring(0, 30)}...` \r\n                          : order.notes[0].note}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </TableCell>\r\n                <TableCell>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Avatar className=\"h-8 w-8\">\r\n                      <AvatarImage src=\"\" />\r\n                      <AvatarFallback>\r\n                        {order.customer?.firstName?.[0]}{order.customer?.lastName?.[0]}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                    <div className=\"flex flex-col\">\r\n                      <span className=\"text-sm font-medium\">\r\n                        {order.customer ? \r\n                          `${order.customer.firstName} ${order.customer.lastName}` : \r\n                          'Guest'\r\n                        }\r\n                      </span>\r\n                      <span className=\"text-xs text-muted-foreground\">\r\n                        {order.customer?.email}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </TableCell>\r\n                <TableCell>\r\n                  <Badge variant=\"outline\" className={getStatusColor(order.status)}>\r\n                    {order.status.replace('_', ' ')}\r\n                  </Badge>\r\n                </TableCell>\r\n                <TableCell>\r\n                  <Badge variant=\"outline\" className={getPaymentStatusColor(\r\n                    order.payments && order.payments.length > 0 \r\n                      ? order.payments[0].status \r\n                      : 'PENDING'\r\n                  )}>\r\n                    {order.payments && order.payments.length > 0 \r\n                      ? order.payments[0].status \r\n                      : 'PENDING'}\r\n                  </Badge>\r\n                </TableCell>\r\n                <TableCell>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Package className=\"h-4 w-4 text-muted-foreground\" />\r\n                    <span className=\"text-sm\">{order.items?.length || 0}</span>\r\n                  </div>\r\n                </TableCell>\r\n                <TableCell className=\"font-medium\">\r\n                  {formatCurrency(order.totalAmount)}\r\n                </TableCell>\r\n                <TableCell>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Calendar className=\"h-4 w-4 text-muted-foreground\" />\r\n                    <span className=\"text-sm\">\r\n                      {format(new Date(order.createdAt), 'MMM dd, yyyy')}\r\n                    </span>\r\n                  </div>\r\n                </TableCell>\r\n                <TableCell>\r\n                  <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                      <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                        <span className=\"sr-only\">Open menu</span>\r\n                        <MoreHorizontal className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent align=\"end\">\r\n                      <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n                      <DropdownMenuItem onClick={() => navigator.clipboard.writeText(order.id)}>\r\n                        Copy order ID\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuSeparator />\r\n                      <DropdownMenuItem onClick={() => onEdit(order)}>\r\n                        <Edit className=\"mr-2 h-4 w-4\" />\r\n                        Edit order\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem onClick={() => onUpdateStatus(order)}>\r\n                        <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n                        Update status\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem>\r\n                        <Eye className=\"mr-2 h-4 w-4\" />\r\n                        View details\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem\r\n                        onClick={async () => {\r\n                          const token = getToken();\r\n                          if (!token) {\r\n                            alert('You must be logged in to view the invoice.');\r\n                            return;\r\n                          }\r\n                          try {\r\n                            const response = await fetch(`${api.baseURL}/orders/${order.id}/invoice`, {\r\n                              headers: {\r\n                                'Authorization': `Bearer ${token}`,\r\n                              },\r\n                            });\r\n                            if (!response.ok) {\r\n                              alert('Failed to fetch invoice');\r\n                              return;\r\n                            }\r\n                            const html = await response.text();\r\n                            const newWindow = window.open();\r\n                            if (newWindow) {\r\n                              newWindow.document.write(html);\r\n                              newWindow.document.close();\r\n                            }\r\n                          } catch (err) {\r\n                            alert('Error fetching invoice');\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Download className=\"mr-2 h-4 w-4\" />\r\n                        Invoice\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuSeparator />\r\n                      <AlertDialog>\r\n                        <AlertDialogTrigger asChild>\r\n                                                     <DropdownMenuItem \r\n                             onSelect={(e: Event) => e.preventDefault()}\r\n                             className=\"text-red-600\"\r\n                           >\r\n                            <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                            Delete order\r\n                          </DropdownMenuItem>\r\n                        </AlertDialogTrigger>\r\n                        <AlertDialogContent>\r\n                          <AlertDialogHeader>\r\n                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>\r\n                            <AlertDialogDescription>\r\n                              This action cannot be undone. This will permanently delete the order\r\n                              and all associated data.\r\n                            </AlertDialogDescription>\r\n                          </AlertDialogHeader>\r\n                          <AlertDialogFooter>\r\n                            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n                            <AlertDialogAction\r\n                              onClick={() => handleDelete(order.id)}\r\n                              className=\"bg-red-600 hover:bg-red-700\"\r\n                            >\r\n                              Delete\r\n                            </AlertDialogAction>\r\n                          </AlertDialogFooter>\r\n                        </AlertDialogContent>\r\n                      </AlertDialog>\r\n                    </DropdownMenuContent>\r\n                  </DropdownMenu>\r\n                </TableCell>\r\n              </TableRow>\r\n            ))}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {totalPages > 1 && (\r\n        <div className=\"flex items-center justify-between px-2\">\r\n          <div className=\"text-sm text-muted-foreground\">\r\n            Page {currentPage} of {totalPages}\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() => onPageChange(Math.max(1, currentPage - 1))}\r\n              disabled={currentPage === 1}\r\n            >\r\n              <ChevronLeft className=\"h-4 w-4\" />\r\n              Previous\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}\r\n              disabled={currentPage === totalPages}\r\n            >\r\n              Next\r\n              <ChevronRight className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAQA;AAWA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AAEA;AAlDA;;;;;;;;;;;;;;;AA+DA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,wBAAwB,CAAC;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,YAAY,MAAe;IAClC,MAAM,UAAU;QAAC;QAAY;QAAY;QAAU;QAAS;KAAO;IACnE,MAAM,OAAO,OAAO,GAAG,CAAC,CAAA,IAAK;YAC3B,EAAE,EAAE;YACJ,EAAE,QAAQ;YACV,EAAE,MAAM;YACR,EAAE,WAAW,EAAE,cAAc;YAC7B,EAAE,SAAS,GAAG,IAAI,KAAK,EAAE,SAAS,EAAE,cAAc,KAAK;SACxD;IACD,OAAO;QAAC;WAAY;KAAK,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;AAC1E;AAEO,SAAS,YAAY,EAC1B,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,cAAc,EACd,WAAW,EACX,UAAU,EACV,YAAY,EACK;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,cAAc,OAAO,MAAM,GAAG,KAAK,SAAS,MAAM,KAAK,OAAO,MAAM;IAC1E,MAAM,kBAAkB;QACtB,IAAI,aAAa,YAAY,EAAE;aAC1B,YAAY,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IACvC;IACA,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,MAAO,IAAI,QAAQ,CAAC,MAAM,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM,MAAM;mBAAI;gBAAK;aAAG;IAChF;IAEA,MAAM,mBAAmB;QACvB,IAAI,SAAS,MAAM,KAAK,GAAG;QAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,MAAM,CAAC,QAAQ,CAAC,GAAG;QAC1D,oCAAoC;QACpC,MAAM,MAAM,2BAA2B;YACrC,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE,KAAK;YAAS;QACvC;QACA,YAAY,EAAE;QACd,cAAc;IACd,uCAAuC;IACzC;IAEA,MAAM,mBAAmB;QACvB,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,SAAS,QAAQ,CAAC,EAAE,EAAE;QAChE,MAAM,MAAM,YAAY;QACxB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAI,EAAE;YAAE,MAAM;QAA0B;QAC/D,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACb,cAAc;IAChB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kDACP,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;;;;;;;sCAGf,8OAAC,iIAAA,CAAA,YAAS;sCACP;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,iIAAA,CAAA,WAAQ;;sDACP,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGxB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;mCAvBT;;;;;;;;;;;;;;;;;;;;;;;;;;IAgC7B;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,8OAAC;oBAAG,WAAU;8BAAiD;;;;;;8BAC/D,8OAAC;oBAAE,WAAU;8BAAqC;;;;;;;;;;;;IAKxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kDACP,8OAAC,iIAAA,CAAA,YAAS;kDAAC,cAAA,8OAAC;4CAAM,MAAK;4CAAW,SAAS;4CAAa,UAAU;;;;;;;;;;;kDAClE,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;;;;;;;;;;;;sCAGpC,8OAAC,iIAAA,CAAA,YAAS;sCACP,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,iIAAA,CAAA,WAAQ;;sDACP,8OAAC,iIAAA,CAAA,YAAS;sDAAC,cAAA,8OAAC;gDAAM,MAAK;gDAAW,SAAS,SAAS,QAAQ,CAAC,MAAM,EAAE;gDAAG,UAAU,IAAM,aAAa,MAAM,EAAE;;;;;;;;;;;sDAC7G,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAAoB;4DAAE,MAAM,WAAW;;;;;;;oDACtD,MAAM,KAAK,IAAI,MAAM,OAAO,CAAC,MAAM,KAAK,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,mBACjE,8OAAC;wDAAK,WAAU;kEACb,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,KAC1B,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAC5C,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI;;;;;;;;;;;;;;;;;sDAK/B,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kIAAA,CAAA,cAAW;gEAAC,KAAI;;;;;;0EACjB,8OAAC,kIAAA,CAAA,iBAAc;;oEACZ,MAAM,QAAQ,EAAE,WAAW,CAAC,EAAE;oEAAE,MAAM,QAAQ,EAAE,UAAU,CAAC,EAAE;;;;;;;;;;;;;kEAGlE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,MAAM,QAAQ,GACb,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,QAAQ,EAAE,GACxD;;;;;;0EAGJ,8OAAC;gEAAK,WAAU;0EACb,MAAM,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;sDAKzB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAW,eAAe,MAAM,MAAM;0DAC5D,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;sDAG/B,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAW,sBAClC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,IACtC,MAAM,QAAQ,CAAC,EAAE,CAAC,MAAM,GACxB;0DAEH,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,IACvC,MAAM,QAAQ,CAAC,EAAE,CAAC,MAAM,GACxB;;;;;;;;;;;sDAGR,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;wDAAK,WAAU;kEAAW,MAAM,KAAK,EAAE,UAAU;;;;;;;;;;;;;;;;;sDAGtD,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,eAAe,MAAM,WAAW;;;;;;sDAEnC,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,SAAS,GAAG;;;;;;;;;;;;;;;;;sDAIzC,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kEACX,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;;8EAChC,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC,gNAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,8OAAC,4IAAA,CAAA,oBAAiB;0EAAC;;;;;;0EACnB,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE;0EAAG;;;;;;0EAG1E,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0EACtB,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,OAAO;;kFACtC,8OAAC,2MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,eAAe;;kFAC9C,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGxC,8OAAC,4IAAA,CAAA,mBAAgB;;kFACf,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,SAAS;oEACP,MAAM,QAAQ,CAAA,GAAA,iHAAA,CAAA,WAAQ,AAAD;oEACrB,IAAI,CAAC,OAAO;wEACV,MAAM;wEACN;oEACF;oEACA,IAAI;wEACF,MAAM,WAAW,MAAM,MAAM,GAAG,iHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE;4EACxE,SAAS;gFACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;4EACpC;wEACF;wEACA,IAAI,CAAC,SAAS,EAAE,EAAE;4EAChB,MAAM;4EACN;wEACF;wEACA,MAAM,OAAO,MAAM,SAAS,IAAI;wEAChC,MAAM,YAAY,OAAO,IAAI;wEAC7B,IAAI,WAAW;4EACb,UAAU,QAAQ,CAAC,KAAK,CAAC;4EACzB,UAAU,QAAQ,CAAC,KAAK;wEAC1B;oEACF,EAAE,OAAO,KAAK;wEACZ,MAAM;oEACR;gEACF;;kFAEA,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0EACtB,8OAAC,2IAAA,CAAA,cAAW;;kFACV,8OAAC,2IAAA,CAAA,qBAAkB;wEAAC,OAAO;kFACE,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;4EACzC,UAAU,CAAC,IAAa,EAAE,cAAc;4EACxC,WAAU;;8FAEX,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;kFAIvC,8OAAC,2IAAA,CAAA,qBAAkB;;0FACjB,8OAAC,2IAAA,CAAA,oBAAiB;;kGAChB,8OAAC,2IAAA,CAAA,mBAAgB;kGAAC;;;;;;kGAClB,8OAAC,2IAAA,CAAA,yBAAsB;kGAAC;;;;;;;;;;;;0FAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;kGAChB,8OAAC,2IAAA,CAAA,oBAAiB;kGAAC;;;;;;kGACnB,8OAAC,2IAAA,CAAA,oBAAiB;wFAChB,SAAS,IAAM,aAAa,MAAM,EAAE;wFACpC,WAAU;kGACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCArJA,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;YAqK9B,aAAa,mBACZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAAgC;4BACvC;4BAAY;4BAAK;;;;;;;kCAEzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,KAAK,GAAG,CAAC,GAAG,cAAc;gCACtD,UAAU,gBAAgB;;kDAE1B,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGrC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,KAAK,GAAG,CAAC,YAAY,cAAc;gCAC/D,UAAU,gBAAgB;;oCAC3B;kDAEC,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 3170, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3372, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3397, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 3622, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3719, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3783, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  className,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n  className?: string\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent\r\n        className={cn(\"overflow-hidden p-0\", className)}\r\n        showCloseButton={showCloseButton}\r\n      >\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,8OAAC,sIAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,SAAS,EACT,kBAAkB,IAAI,EACtB,GAAG,OAMJ;IACC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,8OAAC,kIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,8OAAC,kIAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,8OAAC,kIAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;gBACrC,iBAAiB;0BAEjB,cAAA,8OAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;;0BAEV,8OAAC,0MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3967, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 4036, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/orders/create-order-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n} from '@/components/ui/command';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport { Plus, Trash2, Search, Package, User, CreditCard, ChevronDown, Check } from 'lucide-react';\r\nimport { api, Customer, Product, ProductVariant, Address, Promotion, TaxRate } from '@/lib/api';\r\nimport { toast } from 'sonner';\r\nimport { Country, State } from 'country-state-city';\r\n\r\ninterface CreateOrderDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onSuccess: () => void;\r\n}\r\n\r\ninterface OrderItem {\r\n  variantId: string;\r\n  variant?: ProductVariant & { \r\n    product?: Product;\r\n    inventory?: {\r\n      id: string;\r\n      locationId: string;\r\n      quantity: number;\r\n      reservedQty: number;\r\n    }[];\r\n    segmentPrices?: {\r\n      id: string;\r\n      customerType: 'B2C' | 'B2B' | 'WHOLESALE';\r\n      regularPrice: number;\r\n      salePrice?: number;\r\n    }[];\r\n  };\r\n  quantity: number;\r\n  unitPrice: number;\r\n  totalPrice: number;\r\n}\r\n\r\nexport function CreateOrderDialog({ open, onOpenChange, onSuccess }: CreateOrderDialogProps) {\r\n  const [step, setStep] = useState(1);\r\n  const [loading, setLoading] = useState(false);\r\n  const [customers, setCustomers] = useState<Customer[]>([]);\r\n  const [products, setProducts] = useState<Product[]>([]);\r\n  const [availableCoupons, setAvailableCoupons] = useState<Promotion[]>([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [couponSearchOpen, setCouponSearchOpen] = useState(false);\r\n  \r\n  // Form state\r\n  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);\r\n  const [selectedBillingAddress, setSelectedBillingAddress] = useState<Address | null>(null);\r\n  const [selectedShippingAddress, setSelectedShippingAddress] = useState<Address | null>(null);\r\n  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);\r\n  const [discountAmount, setDiscountAmount] = useState(0);\r\n  const [shippingAmount, setShippingAmount] = useState(0);\r\n  const [taxAmount, setTaxAmount] = useState(0);\r\n  const [notes, setNotes] = useState('');\r\n  const [couponCode, setCouponCode] = useState('');\r\n  const [couponStatus, setCouponStatus] = useState<'idle' | 'valid' | 'invalid' | 'checking'>('idle');\r\n  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);\r\n  const [applicableTaxRate, setApplicableTaxRate] = useState<TaxRate | null>(null);\r\n  const [taxRateLoading, setTaxRateLoading] = useState(false);\r\n  const couponInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Load customers and products\r\n  useEffect(() => {\r\n    if (open) {\r\n      fetchCustomers();\r\n      fetchProducts();\r\n      fetchAvailableCoupons();\r\n    }\r\n  }, [open]);\r\n\r\n  const fetchCustomers = async () => {\r\n    try {\r\n      const response = await api.getCustomers({ limit: 100 });\r\n      if (response.success && response.data) {\r\n        setCustomers(response.data.customers || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to fetch customers:', error);\r\n    }\r\n  };\r\n\r\n  const fetchAvailableCoupons = async () => {\r\n    try {\r\n      const response = await api.getPromotions({ isActive: true, limit: 100 });\r\n      if (response.success && response.data) {\r\n        // Handle both possible response structures\r\n        const coupons = Array.isArray(response.data) ? response.data : response.data.data || [];\r\n        // Filter for currently valid coupons\r\n        const now = new Date();\r\n        const validCoupons = coupons.filter((coupon: Promotion) => {\r\n          const isNotExpired = !coupon.expiresAt || new Date(coupon.expiresAt) >= now;\r\n          const isStarted = !coupon.startsAt || new Date(coupon.startsAt) <= now;\r\n          const hasUsageLeft = !coupon.usageLimit || coupon.usageCount < coupon.usageLimit;\r\n          return coupon.isActive && isNotExpired && isStarted && hasUsageLeft;\r\n        });\r\n        setAvailableCoupons(validCoupons);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to fetch coupons:', error);\r\n    }\r\n  };\r\n\r\n  const fetchProducts = async () => {\r\n    try {\r\n      const response = await api.getProducts({ \r\n        limit: 100, \r\n        status: 'ACTIVE',\r\n        include: {\r\n          variants: {\r\n            include: {\r\n              inventory: true\r\n            }\r\n          }\r\n        }\r\n      });\r\n      \r\n      // Debug logging\r\n      console.log('Products API response:', response);\r\n      \r\n      // Validate inventory data\r\n      const productsWithInventory = (response.success && response.data?.products || []).map((product: Product) => ({\r\n        ...product,\r\n        variants: product.variants?.map((variant: ProductVariant) => ({\r\n          ...variant,\r\n          inventory: variant.inventory || []\r\n        }))\r\n      }));\r\n      \r\n      console.log('Products with validated inventory:', productsWithInventory);\r\n      setProducts(productsWithInventory);\r\n    } catch (error) {\r\n      console.error('Failed to fetch products:', error);\r\n      toast.error('Failed to load products');\r\n    }\r\n  };\r\n\r\n  const handleCustomerSelect = (customerId: string) => {\r\n    const customer = customers.find(c => c.id === customerId);\r\n    setSelectedCustomer(customer || null);\r\n    \r\n    // When customer changes, update prices of existing items based on their segment\r\n    if (customer?.customerType && orderItems.length > 0) {\r\n      setOrderItems(orderItems.map(item => {\r\n        const segmentPrice = item.variant?.segmentPrices?.find(\r\n          sp => sp.customerType === customer.customerType\r\n        );\r\n        \r\n        const newUnitPrice = segmentPrice \r\n          ? (segmentPrice.salePrice || segmentPrice.regularPrice)\r\n          : (item.variant?.salePrice || item.variant?.regularPrice || 0);\r\n\r\n        return {\r\n          ...item,\r\n          unitPrice: newUnitPrice,\r\n          totalPrice: item.quantity * newUnitPrice\r\n        };\r\n      }));\r\n    }\r\n    \r\n    if (customer?.addresses && customer.addresses.length > 0) {\r\n      const billingAddress = customer.addresses.find(a => a.type === 'BILLING' && a.isDefault);\r\n      const shippingAddress = customer.addresses.find(a => a.type === 'SHIPPING' && a.isDefault);\r\n\r\n      setSelectedBillingAddress(billingAddress || customer.addresses[0]);\r\n      const selectedShipping = shippingAddress || customer.addresses[0];\r\n      setSelectedShippingAddress(selectedShipping);\r\n\r\n      // Fetch applicable tax rate for the selected shipping address\r\n      if (selectedShipping) {\r\n        fetchApplicableTaxRate(selectedShipping);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleAddOrderItem = (variant: ProductVariant & { product?: Product }) => {\r\n    // Debug logging\r\n    console.log('Variant inventory:', variant.inventory);\r\n    \r\n    // Check if variant has available inventory\r\n    const totalAvailable = variant.inventory?.reduce((sum, inv) => {\r\n      const available = Math.max(0, (inv.quantity || 0) - (inv.reservedQty || 0));\r\n      console.log(`Location ${inv.locationId}: quantity=${inv.quantity}, reservedQty=${inv.reservedQty}, available=${available}`);\r\n      return sum + available;\r\n    }, 0) || 0;\r\n\r\n    console.log('Total available:', totalAvailable);\r\n\r\n    if (totalAvailable <= 0) {\r\n      toast.error('This product is out of stock');\r\n      return;\r\n    }\r\n\r\n    const existingItem = orderItems.find(item => item.variantId === variant.id);\r\n    \r\n    if (existingItem) {\r\n      // Check if increasing quantity is possible\r\n      if (existingItem.quantity + 1 > totalAvailable) {\r\n        toast.error('Not enough stock available');\r\n        return;\r\n      }\r\n\r\n      setOrderItems(orderItems.map(item =>\r\n        item.variantId === variant.id\r\n          ? { ...item, quantity: item.quantity + 1, totalPrice: Math.max(0, (item.quantity + 1) * item.unitPrice) }\r\n          : item\r\n      ));\r\n    } else {\r\n      // Get price based on customer segment\r\n      const segmentPrice = selectedCustomer?.customerType && variant.segmentPrices?.find(\r\n        sp => sp.customerType === selectedCustomer.customerType\r\n      );\r\n\r\n      const unitPrice = segmentPrice \r\n        ? (segmentPrice.salePrice || segmentPrice.regularPrice)\r\n        : (variant.salePrice || variant.regularPrice);\r\n\r\n      const newItem: OrderItem = {\r\n        variantId: variant.id,\r\n        variant,\r\n        quantity: 1,\r\n        unitPrice,\r\n        totalPrice: Math.max(0, unitPrice),\r\n      };\r\n      setOrderItems([...orderItems, newItem]);\r\n    }\r\n  };\r\n\r\n  const handleRemoveOrderItem = (variantId: string) => {\r\n    setOrderItems(orderItems.filter(item => item.variantId !== variantId));\r\n  };\r\n\r\n  const handleUpdateQuantity = (variantId: string, quantity: number) => {\r\n    if (quantity <= 0) {\r\n      handleRemoveOrderItem(variantId);\r\n      return;\r\n    }\r\n\r\n    const item = orderItems.find(item => item.variantId === variantId);\r\n    if (!item?.variant?.inventory) return;\r\n\r\n    // Check if requested quantity is available\r\n    const totalAvailable = item.variant.inventory.reduce((sum, inv) => \r\n      sum + Math.max(0, inv.quantity - inv.reservedQty), 0);\r\n\r\n    if (quantity > totalAvailable) {\r\n      toast.error('Not enough stock available');\r\n      return;\r\n    }\r\n    \r\n    setOrderItems(orderItems.map(item =>\r\n      item.variantId === variantId\r\n        ? { ...item, quantity, totalPrice: Math.max(0, quantity * item.unitPrice) }\r\n        : item\r\n    ));\r\n  };\r\n\r\n  const handleUpdatePrice = (variantId: string, unitPrice: number) => {\r\n    setOrderItems(orderItems.map(item =>\r\n      item.variantId === variantId\r\n        ? { ...item, unitPrice, totalPrice: Math.max(0, item.quantity * unitPrice) }\r\n        : item\r\n    ));\r\n  };\r\n\r\n  // Convert country and state names to ISO codes for tax rate lookup\r\n  const getCountryStateIsoCodes = (countryName: string, stateName?: string) => {\r\n    // Find country by name\r\n    const country = Country.getAllCountries().find(c =>\r\n      c.name.toLowerCase() === countryName.toLowerCase()\r\n    );\r\n\r\n    if (!country) {\r\n      console.warn(`Country not found: ${countryName}`);\r\n      return { countryCode: null, stateCode: null };\r\n    }\r\n\r\n    let stateCode = null;\r\n    if (stateName) {\r\n      // Find state by name within the country\r\n      const state = State.getStatesOfCountry(country.isoCode).find(s =>\r\n        s.name.toLowerCase() === stateName.toLowerCase()\r\n      );\r\n      stateCode = state?.isoCode || null;\r\n      if (!state) {\r\n        console.warn(`State not found: ${stateName} in ${countryName}`);\r\n      }\r\n    }\r\n\r\n    return { countryCode: country.isoCode, stateCode };\r\n  };\r\n\r\n  // Fetch applicable tax rate based on shipping address\r\n  const fetchApplicableTaxRate = async (address: Address) => {\r\n    if (!address.country) return;\r\n\r\n    setTaxRateLoading(true);\r\n    try {\r\n      // Convert country and state names to ISO codes\r\n      const { countryCode, stateCode } = getCountryStateIsoCodes(address.country, address.state);\r\n\r\n      if (!countryCode) {\r\n        console.error('Could not find country code for:', address.country);\r\n        setApplicableTaxRate(null);\r\n        setTaxAmount(0);\r\n        return;\r\n      }\r\n\r\n      console.log(`Looking up tax rate for: ${countryCode}, ${stateCode || 'no state'}`);\r\n\r\n      const response = await api.getApplicableTaxRate(countryCode, stateCode || undefined);\r\n      if (response.success && response.data) {\r\n        console.log('Found tax rate:', response.data);\r\n        setApplicableTaxRate(response.data);\r\n        // Auto-calculate tax amount based on current subtotal\r\n        const subtotal = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);\r\n        const calculatedTax = subtotal * (response.data.rate / 100);\r\n        setTaxAmount(calculatedTax);\r\n      } else {\r\n        console.log('No tax rate found for:', countryCode, stateCode);\r\n        setApplicableTaxRate(null);\r\n        setTaxAmount(0);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to fetch tax rate:', error);\r\n      setApplicableTaxRate(null);\r\n      setTaxAmount(0);\r\n    } finally {\r\n      setTaxRateLoading(false);\r\n    }\r\n  };\r\n\r\n  const calculateCouponDiscount = async (coupon: Promotion, subtotal: number, shippingAmount: number): Promise<number> => {\r\n    if (!coupon) return 0;\r\n\r\n    // Check minimum order amount\r\n    if (coupon.minOrderAmount && subtotal < parseFloat(coupon.minOrderAmount.toString())) {\r\n      return 0;\r\n    }\r\n\r\n    // For BOGO and VOLUME_DISCOUNT, we need to call the backend for proper calculation\r\n    if (coupon.type === 'BOGO' || coupon.type === 'VOLUME_DISCOUNT') {\r\n      try {\r\n        // Prepare order items data for backend calculation\r\n        const orderItemsData = orderItems.map(item => ({\r\n          variantId: item.variantId,\r\n          quantity: item.quantity,\r\n          unitPrice: item.unitPrice,\r\n          variant: {\r\n            productId: item.variant?.productId\r\n          }\r\n        }));\r\n\r\n        // Call backend to calculate advanced promotion discount\r\n        const response = await api.calculatePromotionDiscount({\r\n          promotionCode: coupon.code,\r\n          orderItems: orderItemsData,\r\n          customerId: selectedCustomer?.id,\r\n          subtotal,\r\n          shippingAmount\r\n        });\r\n\r\n        if (response.success && response.data) {\r\n          return response.data.discount || 0;\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to calculate advanced promotion discount:', error);\r\n        // Fallback to simple calculation below\r\n      }\r\n    }\r\n\r\n    // Simple calculation for basic promotion types or fallback\r\n    let discount = 0;\r\n\r\n    switch (coupon.type) {\r\n      case 'PERCENTAGE':\r\n        discount = subtotal * (parseFloat(coupon.value.toString()) / 100);\r\n        break;\r\n      case 'FIXED_AMOUNT':\r\n        discount = parseFloat(coupon.value.toString());\r\n        break;\r\n      case 'FREE_SHIPPING':\r\n        discount = shippingAmount;\r\n        break;\r\n      case 'BOGO':\r\n        // Fallback simple BOGO logic if backend call fails\r\n        const totalQuantity = orderItems.reduce((sum, item) => sum + item.quantity, 0);\r\n\r\n        // Default to Buy 2 Get 1 Free if no specific configuration\r\n        const buyQty = 2;\r\n        const getQty = 1;\r\n        const freeItems = Math.floor(totalQuantity / buyQty) * getQty;\r\n\r\n        if (freeItems > 0) {\r\n          // Calculate discount based on cheapest items getting free\r\n          const sortedItems = [...orderItems].sort((a, b) => a.unitPrice - b.unitPrice);\r\n          let remainingFreeQty = freeItems;\r\n\r\n          for (const item of sortedItems) {\r\n            if (remainingFreeQty <= 0) break;\r\n            const freeQtyForItem = Math.min(remainingFreeQty, item.quantity);\r\n            discount += freeQtyForItem * item.unitPrice;\r\n            remainingFreeQty -= freeQtyForItem;\r\n          }\r\n        }\r\n        break;\r\n      case 'VOLUME_DISCOUNT':\r\n        // Fallback volume discount logic\r\n        const totalQty = orderItems.reduce((sum, item) => sum + item.quantity, 0);\r\n        if (totalQty >= 10) {\r\n          discount = subtotal * 0.1; // 10% for 10+ items\r\n        } else if (totalQty >= 5) {\r\n          discount = subtotal * 0.05; // 5% for 5+ items\r\n        }\r\n        break;\r\n      default:\r\n        discount = 0;\r\n    }\r\n\r\n    // Apply maximum discount limit if set\r\n    if (coupon.maxDiscount) {\r\n      discount = Math.min(discount, parseFloat(coupon.maxDiscount.toString()));\r\n    }\r\n\r\n    // Don't exceed subtotal\r\n    return Math.min(discount, subtotal);\r\n  };\r\n\r\n  const handleCouponSelect = async (coupon: Promotion | null) => {\r\n    if (coupon) {\r\n      setCouponCode(coupon.code);\r\n      setAppliedCoupon(coupon);\r\n      setCouponStatus('valid');\r\n\r\n      // Calculate and apply discount\r\n      const subtotal = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);\r\n      const calculatedDiscount = await calculateCouponDiscount(coupon, subtotal, shippingAmount);\r\n      setDiscountAmount(calculatedDiscount);\r\n\r\n      toast.success(`Coupon \"${coupon.code}\" applied! Discount: $${calculatedDiscount.toFixed(2)}`);\r\n    } else {\r\n      setCouponCode('');\r\n      setAppliedCoupon(null);\r\n      setCouponStatus('idle');\r\n      setDiscountAmount(0);\r\n    }\r\n    setCouponSearchOpen(false);\r\n  };\r\n\r\n  const handleValidateCoupon = async () => {\r\n    if (!couponCode) return;\r\n    setCouponStatus('checking');\r\n    setAppliedCoupon(null);\r\n    try {\r\n      const res = await api.validateCoupon(couponCode);\r\n      if (res.success && res.data) {\r\n        handleCouponSelect(res.data);\r\n      } else {\r\n        setCouponStatus('invalid');\r\n        setAppliedCoupon(null);\r\n        setDiscountAmount(0);\r\n        toast.error(res.error || 'Invalid or expired coupon code');\r\n      }\r\n    } catch (e: any) {\r\n      setCouponStatus('invalid');\r\n      setAppliedCoupon(null);\r\n      setDiscountAmount(0);\r\n      toast.error(e.message || 'Invalid or expired coupon code');\r\n    }\r\n  };\r\n\r\n  // Calculate totals\r\n  const subtotal = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);\r\n\r\n  // Recalculate discount when subtotal changes and coupon is applied\r\n  useEffect(() => {\r\n    const recalculateDiscount = async () => {\r\n      if (appliedCoupon && subtotal > 0) {\r\n        const newDiscount = await calculateCouponDiscount(appliedCoupon, subtotal, shippingAmount);\r\n        setDiscountAmount(newDiscount);\r\n      }\r\n    };\r\n\r\n    recalculateDiscount();\r\n  }, [subtotal, shippingAmount, appliedCoupon]);\r\n\r\n  // Recalculate tax when subtotal changes and tax rate is available\r\n  useEffect(() => {\r\n    if (applicableTaxRate && subtotal > 0) {\r\n      const calculatedTax = subtotal * (applicableTaxRate.rate / 100);\r\n      setTaxAmount(calculatedTax);\r\n    }\r\n  }, [subtotal, applicableTaxRate]);\r\n\r\n  const totalAmount = subtotal - discountAmount + shippingAmount + taxAmount;\r\n\r\n  const handleSubmit = async () => {\r\n    if (!selectedCustomer) {\r\n      toast.error('Please select a customer');\r\n      return;\r\n    }\r\n\r\n    if (!selectedBillingAddress || !selectedShippingAddress) {\r\n      toast.error('Please select billing and shipping addresses');\r\n      return;\r\n    }\r\n\r\n    if (orderItems.length === 0) {\r\n      toast.error('Please add at least one item');\r\n      return;\r\n    }\r\n\r\n    // Validate inventory availability one final time\r\n    for (const item of orderItems) {\r\n      if (!item.variant?.inventory) {\r\n        toast.error('Invalid product variant');\r\n        return;\r\n      }\r\n\r\n      const totalAvailable = item.variant.inventory.reduce((sum, inv) => \r\n        sum + Math.max(0, inv.quantity - inv.reservedQty), 0);\r\n\r\n      if (item.quantity > totalAvailable) {\r\n        toast.error(`Not enough stock available for ${item.variant.product?.name} - ${item.variant.name}`);\r\n        return;\r\n      }\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      \r\n      const orderData = {\r\n        customerId: selectedCustomer.id,\r\n        billingAddressId: selectedBillingAddress.id,\r\n        shippingAddressId: selectedShippingAddress.id,\r\n        items: orderItems.map(item => ({\r\n          variantId: item.variantId,\r\n          quantity: item.quantity,\r\n          unitPrice: item.unitPrice.toFixed(2),\r\n        })),\r\n        discountAmount: discountAmount.toFixed(2),\r\n        shippingAmount: shippingAmount.toFixed(2),\r\n        taxAmount: taxAmount.toFixed(2),\r\n        couponCode: appliedCoupon ? appliedCoupon.code : undefined,\r\n      };\r\n\r\n      const response = await api.createOrder(orderData);\r\n      \r\n      if (response.success) {\r\n        // Add note if provided\r\n        if (notes.trim() && response.data) {\r\n          await api.addOrderNote(response.data.id, notes.trim());\r\n        }\r\n        \r\n        toast.success('Order created successfully');\r\n        resetForm();\r\n        onSuccess();\r\n        onOpenChange(false);\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Failed to create order:', error);\r\n\r\n      // Show specific validation errors if available\r\n      if (error.response?.data?.errors) {\r\n        const validationErrors = error.response.data.errors;\r\n        toast.error(`Validation failed: ${validationErrors.map((e: any) => e.msg).join(', ')}`);\r\n      } else {\r\n        toast.error(error?.message || 'Failed to create order');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setStep(1);\r\n    setSelectedCustomer(null);\r\n    setSelectedBillingAddress(null);\r\n    setSelectedShippingAddress(null);\r\n    setOrderItems([]);\r\n    setDiscountAmount(0);\r\n    setShippingAmount(0);\r\n    setTaxAmount(0);\r\n    setNotes('');\r\n    setCouponCode('');\r\n    setCouponStatus('idle');\r\n    setAppliedCoupon(null);\r\n    setCouponSearchOpen(false);\r\n    setApplicableTaxRate(null);\r\n    setTaxRateLoading(false);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    if (!loading) {\r\n      resetForm();\r\n      onOpenChange(false);\r\n    }\r\n  };\r\n\r\n  const filteredProducts = products.filter(product =>\r\n    product.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  const canProceedToStep2 = selectedCustomer && selectedBillingAddress && selectedShippingAddress;\r\n  const canProceedToStep3 = orderItems.length > 0;\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={handleClose}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle>Create New Order</DialogTitle>\r\n          <DialogDescription>\r\n            Follow the steps to create a new order\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <Tabs value={step.toString()} onValueChange={(value) => setStep(parseInt(value))}>\r\n          <TabsList className=\"grid w-full grid-cols-3\">\r\n            <TabsTrigger value=\"1\" className=\"flex items-center gap-2\">\r\n              <User className=\"w-4 h-4\" />\r\n              Customer\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"2\" disabled={!canProceedToStep2} className=\"flex items-center gap-2\">\r\n              <Package className=\"w-4 h-4\" />\r\n              Products\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"3\" disabled={!canProceedToStep3} className=\"flex items-center gap-2\">\r\n              <CreditCard className=\"w-4 h-4\" />\r\n              Review\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"1\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>Select Customer</CardTitle>\r\n                <CardDescription>Choose the customer for this order</CardDescription>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                <div className=\"grid gap-4\">\r\n                  <div>\r\n                    <Label htmlFor=\"customer\">Customer</Label>\r\n                    <Select value={selectedCustomer?.id || ''} onValueChange={handleCustomerSelect}>\r\n                      <SelectTrigger>\r\n                        <SelectValue placeholder=\"Select a customer\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {customers.map((customer) => (\r\n                          <SelectItem key={customer.id} value={customer.id}>\r\n                            {customer.firstName} {customer.lastName} - {customer.email}\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n\r\n                  {selectedCustomer && (\r\n                    <div className=\"grid gap-4 md:grid-cols-2\">\r\n                      <div>\r\n                        <Label htmlFor=\"billingAddress\">Billing Address</Label>\r\n                        <Select \r\n                          value={selectedBillingAddress?.id || ''} \r\n                          onValueChange={(value) => {\r\n                            const address = selectedCustomer.addresses?.find(a => a.id === value);\r\n                            setSelectedBillingAddress(address || null);\r\n                          }}\r\n                        >\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select billing address\" />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            {selectedCustomer.addresses?.map((address) => (\r\n                              <SelectItem key={address.id} value={address.id}>\r\n                                {address.firstName} {address.lastName} - {address.address1}, {address.city}\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                      </div>\r\n\r\n                      <div>\r\n                        <Label htmlFor=\"shippingAddress\">Shipping Address</Label>\r\n                        <Select\r\n                          value={selectedShippingAddress?.id || ''}\r\n                          onValueChange={(value) => {\r\n                            const address = selectedCustomer.addresses?.find(a => a.id === value);\r\n                            setSelectedShippingAddress(address || null);\r\n                            // Fetch applicable tax rate when shipping address changes\r\n                            if (address) {\r\n                              fetchApplicableTaxRate(address);\r\n                            } else {\r\n                              setApplicableTaxRate(null);\r\n                              setTaxAmount(0);\r\n                            }\r\n                          }}\r\n                        >\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select shipping address\" />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            {selectedCustomer.addresses?.map((address) => (\r\n                              <SelectItem key={address.id} value={address.id}>\r\n                                {address.firstName} {address.lastName} - {address.address1}, {address.city}\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"flex justify-end\">\r\n                  <Button\r\n                    onClick={() => setStep(2)}\r\n                    disabled={!canProceedToStep2}\r\n                  >\r\n                    Next: Add Products\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"2\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>Add Products</CardTitle>\r\n                <CardDescription>Search and add products to the order</CardDescription>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                <div className=\"relative\">\r\n                  <Search className=\"absolute left-3 top-3 h-4 w-4 text-muted-foreground\" />\r\n                  <Input\r\n                    placeholder=\"Search products...\"\r\n                    value={searchTerm}\r\n                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                    className=\"pl-10\"\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"grid gap-4 max-h-60 overflow-y-auto\">\r\n                  {filteredProducts.map((product) => (\r\n                    <div key={product.id} className=\"border rounded-lg p-4\">\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div>\r\n                          <h4 className=\"font-medium\">{product.name}</h4>\r\n                          <p className=\"text-sm text-muted-foreground\">{product.description}</p>\r\n                        </div>\r\n                        <Badge variant=\"outline\">{product.variants?.length || 0} variants</Badge>\r\n                      </div>\r\n                      \r\n                      {product.variants && product.variants.length > 0 && (\r\n                        <div className=\"mt-3 space-y-2\">\r\n                          {product.variants.map((variant) => {\r\n                            // Debug logging\r\n                            console.log('Rendering variant:', variant.name, 'inventory:', variant.inventory);\r\n                            \r\n                            const totalAvailable = variant.inventory?.reduce((sum, inv) => {\r\n                              const available = Math.max(0, (inv.quantity || 0) - (inv.reservedQty || 0));\r\n                              console.log(`Location ${inv.locationId}: quantity=${inv.quantity}, reservedQty=${inv.reservedQty}, available=${available}`);\r\n                              return sum + available;\r\n                            }, 0) || 0;\r\n\r\n                            console.log('Total available for display:', totalAvailable);\r\n\r\n                            // Get segment-specific price for display\r\n                            const segmentPrice = selectedCustomer?.customerType && variant.segmentPrices?.find(\r\n                              sp => sp.customerType === selectedCustomer.customerType\r\n                            );\r\n\r\n                            const displayPrice = segmentPrice \r\n                              ? (segmentPrice.salePrice || segmentPrice.regularPrice)\r\n                              : (variant.salePrice || variant.regularPrice);\r\n\r\n                            const priceText = segmentPrice \r\n                              ? `$${displayPrice} (${selectedCustomer.customerType} price)`\r\n                              : `$${displayPrice}`;\r\n\r\n                            return (\r\n                              <div key={variant.id} className=\"flex items-center justify-between p-2 border rounded\">\r\n                                <div className=\"flex-1\">\r\n                                  <div className=\"font-medium\">{variant.name}</div>\r\n                                  <div className=\"text-sm text-muted-foreground\">\r\n                                    SKU: {variant.sku} • {priceText}\r\n                                    <span className=\"ml-2\">\r\n                                      {totalAvailable > 0 ? (\r\n                                        <Badge variant=\"outline\" className=\"ml-2\">\r\n                                          {totalAvailable} in stock\r\n                                        </Badge>\r\n                                      ) : (\r\n                                        <Badge variant=\"destructive\" className=\"ml-2\">\r\n                                          Out of stock\r\n                                        </Badge>\r\n                                      )}\r\n                                    </span>\r\n                                  </div>\r\n                                </div>\r\n                                <Button\r\n                                  size=\"sm\"\r\n                                  onClick={() => handleAddOrderItem(variant)}\r\n                                  disabled={totalAvailable <= 0 || orderItems.some(item => item.variantId === variant.id)}\r\n                                >\r\n                                  <Plus className=\"w-4 h-4 mr-1\" />\r\n                                  Add\r\n                                </Button>\r\n                              </div>\r\n                            );\r\n                          })}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n\r\n                <div className=\"flex justify-between\">\r\n                  <Button variant=\"outline\" onClick={() => setStep(1)}>\r\n                    Back\r\n                  </Button>\r\n                  <Button\r\n                    onClick={() => setStep(3)}\r\n                    disabled={!canProceedToStep3}\r\n                  >\r\n                    Next: Review Order\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"3\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>Review Order</CardTitle>\r\n                <CardDescription>Review and finalize the order details</CardDescription>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                {/* Order Items */}\r\n                <div className=\"space-y-2\">\r\n                  <h4 className=\"font-medium\">Order Items</h4>\r\n                  {orderItems.map((item) => (\r\n                    <div key={item.variantId} className=\"flex items-center justify-between p-3 border rounded\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"font-medium\">{item.variant?.product?.name} - {item.variant?.name}</div>\r\n                        <div className=\"text-sm text-muted-foreground\">SKU: {item.variant?.sku}</div>\r\n                      </div>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Input\r\n                          type=\"number\"\r\n                          value={item.quantity}\r\n                          onChange={(e) => handleUpdateQuantity(item.variantId, parseInt(e.target.value) || 0)}\r\n                          className=\"w-16 text-center\"\r\n                          min=\"1\"\r\n                        />\r\n                        <span className=\"text-sm\">×</span>\r\n                        <Input\r\n                          type=\"number\"\r\n                          value={item.unitPrice}\r\n                          onChange={(e) => handleUpdatePrice(item.variantId, parseFloat(e.target.value) || 0)}\r\n                          className=\"w-20 text-center\"\r\n                          min=\"0\"\r\n                          step=\"0.01\"\r\n                        />\r\n                        <span className=\"text-sm font-medium\">${(item.totalPrice || 0).toFixed(2)}</span>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={() => handleRemoveOrderItem(item.variantId)}\r\n                        >\r\n                          <Trash2 className=\"w-4 h-4\" />\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Order Summary */}\r\n                <div className=\"space-y-2\">\r\n                  <h4 className=\"font-medium\">Order Summary</h4>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Subtotal:</span>\r\n                      <span>${subtotal.toFixed(2)}</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span>Discount:</span>\r\n                      <Input\r\n                        type=\"number\"\r\n                        value={discountAmount}\r\n                        onChange={(e) => setDiscountAmount(parseFloat(e.target.value) || 0)}\r\n                        className=\"w-24 text-right\"\r\n                        min=\"0\"\r\n                        step=\"0.01\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span>Shipping:</span>\r\n                      <Input\r\n                        type=\"number\"\r\n                        value={shippingAmount}\r\n                        onChange={(e) => setShippingAmount(parseFloat(e.target.value) || 0)}\r\n                        className=\"w-24 text-right\"\r\n                        min=\"0\"\r\n                        step=\"0.01\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"space-y-2\">\r\n                      <div className=\"flex justify-between items-center\">\r\n                        <div className=\"flex flex-col\">\r\n                          <span>Tax:</span>\r\n                          {applicableTaxRate && (\r\n                            <span className=\"text-xs text-muted-foreground\">\r\n                              {applicableTaxRate.country}{applicableTaxRate.state ? `, ${applicableTaxRate.state}` : ''} - {applicableTaxRate.rate}% ({applicableTaxRate.type})\r\n                            </span>\r\n                          )}\r\n                          {taxRateLoading && (\r\n                            <span className=\"text-xs text-muted-foreground\">Loading tax rate...</span>\r\n                          )}\r\n                          {!applicableTaxRate && !taxRateLoading && selectedShippingAddress && (\r\n                            <span className=\"text-xs text-muted-foreground\">No tax rate found</span>\r\n                          )}\r\n                        </div>\r\n                        <Input\r\n                          type=\"number\"\r\n                          value={taxAmount}\r\n                          onChange={(e) => setTaxAmount(parseFloat(e.target.value) || 0)}\r\n                          className=\"w-24 text-right\"\r\n                          min=\"0\"\r\n                          step=\"0.01\"\r\n                          placeholder=\"0.00\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex justify-between font-medium text-lg pt-2 border-t\">\r\n                      <span>Total:</span>\r\n                      <span>${totalAmount.toFixed(2)}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Notes */}\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"notes\">Order Notes (Optional)</Label>\r\n                  <Textarea\r\n                    id=\"notes\"\r\n                    placeholder=\"Add any notes about this order...\"\r\n                    value={notes}\r\n                    onChange={(e) => setNotes(e.target.value)}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <Label>Coupon Code</Label>\r\n                  <div className=\"flex gap-2\">\r\n                    <Popover open={couponSearchOpen} onOpenChange={setCouponSearchOpen}>\r\n                      <PopoverTrigger asChild>\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          role=\"combobox\"\r\n                          aria-expanded={couponSearchOpen}\r\n                          className=\"w-48 justify-between\"\r\n                        >\r\n                          {appliedCoupon ? appliedCoupon.code : \"Select coupon...\"}\r\n                          <ChevronDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n                        </Button>\r\n                      </PopoverTrigger>\r\n                      <PopoverContent className=\"w-80 p-0\">\r\n                        <Command>\r\n                          <CommandInput placeholder=\"Search coupons...\" />\r\n                          <CommandEmpty>No coupons found.</CommandEmpty>\r\n                          <CommandGroup>\r\n                            {availableCoupons.map((coupon) => {\r\n                              // Simple discount calculation for display (not async)\r\n                              let discount = 0;\r\n                              const isEligible = !coupon.minOrderAmount || subtotal >= parseFloat(coupon.minOrderAmount.toString());\r\n\r\n                              if (isEligible) {\r\n                                switch (coupon.type) {\r\n                                  case 'PERCENTAGE':\r\n                                    discount = subtotal * (parseFloat(coupon.value.toString()) / 100);\r\n                                    break;\r\n                                  case 'FIXED_AMOUNT':\r\n                                    discount = parseFloat(coupon.value.toString());\r\n                                    break;\r\n                                  case 'FREE_SHIPPING':\r\n                                    discount = shippingAmount;\r\n                                    break;\r\n                                  case 'BOGO':\r\n                                    discount = subtotal * 0.25; // Estimated discount for display\r\n                                    break;\r\n                                  case 'VOLUME_DISCOUNT':\r\n                                    discount = subtotal * 0.1; // Estimated discount for display\r\n                                    break;\r\n                                }\r\n\r\n                                if (coupon.maxDiscount) {\r\n                                  discount = Math.min(discount, parseFloat(coupon.maxDiscount.toString()));\r\n                                }\r\n                                discount = Math.min(discount, subtotal);\r\n                              }\r\n\r\n                              return (\r\n                                <CommandItem\r\n                                  key={coupon.id}\r\n                                  value={coupon.code}\r\n                                  onSelect={() => handleCouponSelect(coupon)}\r\n                                  disabled={!isEligible}\r\n                                  className={!isEligible ? 'opacity-50' : ''}\r\n                                >\r\n                                  <Check\r\n                                    className={`mr-2 h-4 w-4 ${\r\n                                      appliedCoupon?.id === coupon.id ? \"opacity-100\" : \"opacity-0\"\r\n                                    }`}\r\n                                  />\r\n                                  <div className=\"flex-1\">\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                      <span className=\"font-medium\">{coupon.code}</span>\r\n                                      <span className=\"text-sm text-green-600\">\r\n                                        {isEligible ? (\r\n                                          coupon.type === 'BOGO' ? (\r\n                                            <div className=\"text-right\">\r\n                                              <div>{`~$${discount.toFixed(2)}`}</div>\r\n                                              <div className=\"text-xs text-gray-500\">\r\n                                                {(() => {\r\n                                                  const totalQty = orderItems.reduce((sum, item) => sum + item.quantity, 0);\r\n                                                  const buyQty = (coupon as any).buyQuantity || 2;\r\n                                                  const getQty = (coupon as any).getQuantity || 1;\r\n                                                  const freeItems = Math.floor(totalQty / buyQty) * getQty;\r\n                                                  return `${freeItems} free item${freeItems !== 1 ? 's' : ''}`;\r\n                                                })()}\r\n                                              </div>\r\n                                            </div>\r\n                                          ) : coupon.type === 'VOLUME_DISCOUNT' ? (\r\n                                            `~$${discount.toFixed(2)}`\r\n                                          ) : (\r\n                                            `-$${discount.toFixed(2)}`\r\n                                          )\r\n                                        ) : 'Not eligible'}\r\n                                      </span>\r\n                                    </div>\r\n                                    <div className=\"text-sm text-muted-foreground\">\r\n                                      {coupon.name}\r\n                                      {coupon.minOrderAmount && (\r\n                                        <span className=\"ml-2\">\r\n                                          (Min: ${parseFloat(coupon.minOrderAmount.toString()).toFixed(2)})\r\n                                        </span>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                </CommandItem>\r\n                              );\r\n                            })}\r\n                          </CommandGroup>\r\n                        </Command>\r\n                      </PopoverContent>\r\n                    </Popover>\r\n\r\n                    <div className=\"flex-1\">\r\n                      <Input\r\n                        ref={couponInputRef}\r\n                        value={couponCode}\r\n                        onChange={e => setCouponCode(e.target.value)}\r\n                        placeholder=\"Or enter coupon code\"\r\n                        className=\"w-full\"\r\n                      />\r\n                    </div>\r\n\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      onClick={handleValidateCoupon}\r\n                      disabled={couponStatus === 'checking' || !couponCode}\r\n                    >\r\n                      {couponStatus === 'checking' ? 'Checking...' : 'Apply'}\r\n                    </Button>\r\n\r\n                    {appliedCoupon && (\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"outline\"\r\n                        onClick={() => handleCouponSelect(null)}\r\n                        size=\"sm\"\r\n                      >\r\n                        Clear\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n\r\n                  {couponStatus === 'valid' && appliedCoupon && (\r\n                    <div className=\"text-green-600 text-sm bg-green-50 p-2 rounded\">\r\n                      <div className=\"font-medium\">✓ Coupon applied: {appliedCoupon.code}</div>\r\n                      <div>{appliedCoupon.name}</div>\r\n                      <div>Discount: ${discountAmount.toFixed(2)}</div>\r\n\r\n                      {appliedCoupon.type === 'BOGO' && (\r\n                        <div className=\"mt-1 text-blue-600\">\r\n                          {(() => {\r\n                            const totalQty = orderItems.reduce((sum, item) => sum + item.quantity, 0);\r\n                            const buyQty = (appliedCoupon as any).buyQuantity || 2;\r\n                            const getQty = (appliedCoupon as any).getQuantity || 1;\r\n                            const freeItems = Math.floor(totalQty / buyQty) * getQty;\r\n                            const nextFreeAt = Math.ceil(totalQty / buyQty) * buyQty;\r\n\r\n                            return (\r\n                              <div>\r\n                                <div>🎁 {freeItems} free item{freeItems !== 1 ? 's' : ''} earned</div>\r\n                                {freeItems === 0 && (\r\n                                  <div className=\"text-orange-600\">\r\n                                    Add {buyQty - totalQty} more item{buyQty - totalQty !== 1 ? 's' : ''} to get {getQty} free\r\n                                  </div>\r\n                                )}\r\n                                {freeItems > 0 && totalQty < nextFreeAt && (\r\n                                  <div className=\"text-gray-600\">\r\n                                    Add {nextFreeAt - totalQty} more for next free item\r\n                                  </div>\r\n                                )}\r\n                              </div>\r\n                            );\r\n                          })()}\r\n                        </div>\r\n                      )}\r\n\r\n                      {appliedCoupon.minOrderAmount && subtotal < parseFloat(appliedCoupon.minOrderAmount.toString()) && (\r\n                        <div className=\"text-orange-600\">\r\n                          Add ${(parseFloat(appliedCoupon.minOrderAmount.toString()) - subtotal).toFixed(2)} more to qualify\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n\r\n                  {couponStatus === 'invalid' && (\r\n                    <div className=\"text-red-600 text-sm bg-red-50 p-2 rounded\">\r\n                      Invalid or expired coupon code\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"flex justify-between\">\r\n                  <Button variant=\"outline\" onClick={() => setStep(2)}>\r\n                    Back\r\n                  </Button>\r\n                  <Button onClick={handleSubmit} disabled={loading}>\r\n                    {loading ? 'Creating Order...' : 'Create Order'}\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAlCA;;;;;;;;;;;;;;;;;;AAgEO,SAAS,kBAAkB,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAA0B;IACzF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,aAAa;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrF,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAC5F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAI;YACrD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,aAAa,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,aAAa,CAAC;gBAAE,UAAU;gBAAM,OAAO;YAAI;YACtE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,2CAA2C;gBAC3C,MAAM,UAAU,MAAM,OAAO,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;gBACvF,qCAAqC;gBACrC,MAAM,MAAM,IAAI;gBAChB,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC;oBACnC,MAAM,eAAe,CAAC,OAAO,SAAS,IAAI,IAAI,KAAK,OAAO,SAAS,KAAK;oBACxE,MAAM,YAAY,CAAC,OAAO,QAAQ,IAAI,IAAI,KAAK,OAAO,QAAQ,KAAK;oBACnE,MAAM,eAAe,CAAC,OAAO,UAAU,IAAI,OAAO,UAAU,GAAG,OAAO,UAAU;oBAChF,OAAO,OAAO,QAAQ,IAAI,gBAAgB,aAAa;gBACzD;gBACA,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,WAAW,CAAC;gBACrC,OAAO;gBACP,QAAQ;gBACR,SAAS;oBACP,UAAU;wBACR,SAAS;4BACP,WAAW;wBACb;oBACF;gBACF;YACF;YAEA,gBAAgB;YAChB,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,0BAA0B;YAC1B,MAAM,wBAAwB,CAAC,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,YAAY,EAAE,EAAE,GAAG,CAAC,CAAC,UAAqB,CAAC;oBAC3G,GAAG,OAAO;oBACV,UAAU,QAAQ,QAAQ,EAAE,IAAI,CAAC,UAA4B,CAAC;4BAC5D,GAAG,OAAO;4BACV,WAAW,QAAQ,SAAS,IAAI,EAAE;wBACpC,CAAC;gBACH,CAAC;YAED,QAAQ,GAAG,CAAC,sCAAsC;YAClD,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,oBAAoB,YAAY;QAEhC,gFAAgF;QAChF,IAAI,UAAU,gBAAgB,WAAW,MAAM,GAAG,GAAG;YACnD,cAAc,WAAW,GAAG,CAAC,CAAA;gBAC3B,MAAM,eAAe,KAAK,OAAO,EAAE,eAAe,KAChD,CAAA,KAAM,GAAG,YAAY,KAAK,SAAS,YAAY;gBAGjD,MAAM,eAAe,eAChB,aAAa,SAAS,IAAI,aAAa,YAAY,GACnD,KAAK,OAAO,EAAE,aAAa,KAAK,OAAO,EAAE,gBAAgB;gBAE9D,OAAO;oBACL,GAAG,IAAI;oBACP,WAAW;oBACX,YAAY,KAAK,QAAQ,GAAG;gBAC9B;YACF;QACF;QAEA,IAAI,UAAU,aAAa,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;YACxD,MAAM,iBAAiB,SAAS,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,SAAS;YACvF,MAAM,kBAAkB,SAAS,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc,EAAE,SAAS;YAEzF,0BAA0B,kBAAkB,SAAS,SAAS,CAAC,EAAE;YACjE,MAAM,mBAAmB,mBAAmB,SAAS,SAAS,CAAC,EAAE;YACjE,2BAA2B;YAE3B,8DAA8D;YAC9D,IAAI,kBAAkB;gBACpB,uBAAuB;YACzB;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;QAChB,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,SAAS;QAEnD,2CAA2C;QAC3C,MAAM,iBAAiB,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK;YACrD,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC;YACzE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,UAAU,CAAC,WAAW,EAAE,IAAI,QAAQ,CAAC,cAAc,EAAE,IAAI,WAAW,CAAC,YAAY,EAAE,WAAW;YAC1H,OAAO,MAAM;QACf,GAAG,MAAM;QAET,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,IAAI,kBAAkB,GAAG;YACvB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK,QAAQ,EAAE;QAE1E,IAAI,cAAc;YAChB,2CAA2C;YAC3C,IAAI,aAAa,QAAQ,GAAG,IAAI,gBAAgB;gBAC9C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,cAAc,WAAW,GAAG,CAAC,CAAA,OAC3B,KAAK,SAAS,KAAK,QAAQ,EAAE,GACzB;oBAAE,GAAG,IAAI;oBAAE,UAAU,KAAK,QAAQ,GAAG;oBAAG,YAAY,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,GAAG,CAAC,IAAI,KAAK,SAAS;gBAAE,IACtG;QAER,OAAO;YACL,sCAAsC;YACtC,MAAM,eAAe,kBAAkB,gBAAgB,QAAQ,aAAa,EAAE,KAC5E,CAAA,KAAM,GAAG,YAAY,KAAK,iBAAiB,YAAY;YAGzD,MAAM,YAAY,eACb,aAAa,SAAS,IAAI,aAAa,YAAY,GACnD,QAAQ,SAAS,IAAI,QAAQ,YAAY;YAE9C,MAAM,UAAqB;gBACzB,WAAW,QAAQ,EAAE;gBACrB;gBACA,UAAU;gBACV;gBACA,YAAY,KAAK,GAAG,CAAC,GAAG;YAC1B;YACA,cAAc;mBAAI;gBAAY;aAAQ;QACxC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,cAAc,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;IAC7D;IAEA,MAAM,uBAAuB,CAAC,WAAmB;QAC/C,IAAI,YAAY,GAAG;YACjB,sBAAsB;YACtB;QACF;QAEA,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;QACxD,IAAI,CAAC,MAAM,SAAS,WAAW;QAE/B,2CAA2C;QAC3C,MAAM,iBAAiB,KAAK,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,MACzD,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,QAAQ,GAAG,IAAI,WAAW,GAAG;QAErD,IAAI,WAAW,gBAAgB;YAC7B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,cAAc,WAAW,GAAG,CAAC,CAAA,OAC3B,KAAK,SAAS,KAAK,YACf;gBAAE,GAAG,IAAI;gBAAE;gBAAU,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW,KAAK,SAAS;YAAE,IACxE;IAER;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,cAAc,WAAW,GAAG,CAAC,CAAA,OAC3B,KAAK,SAAS,KAAK,YACf;gBAAE,GAAG,IAAI;gBAAE;gBAAW,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,GAAG;YAAW,IACzE;IAER;IAEA,mEAAmE;IACnE,MAAM,0BAA0B,CAAC,aAAqB;QACpD,uBAAuB;QACvB,MAAM,UAAU,gMAAA,CAAA,UAAO,CAAC,eAAe,GAAG,IAAI,CAAC,CAAA,IAC7C,EAAE,IAAI,CAAC,WAAW,OAAO,YAAY,WAAW;QAGlD,IAAI,CAAC,SAAS;YACZ,QAAQ,IAAI,CAAC,CAAC,mBAAmB,EAAE,aAAa;YAChD,OAAO;gBAAE,aAAa;gBAAM,WAAW;YAAK;QAC9C;QAEA,IAAI,YAAY;QAChB,IAAI,WAAW;YACb,wCAAwC;YACxC,MAAM,QAAQ,4LAAA,CAAA,QAAK,CAAC,kBAAkB,CAAC,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAA,IAC3D,EAAE,IAAI,CAAC,WAAW,OAAO,UAAU,WAAW;YAEhD,YAAY,OAAO,WAAW;YAC9B,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,UAAU,IAAI,EAAE,aAAa;YAChE;QACF;QAEA,OAAO;YAAE,aAAa,QAAQ,OAAO;YAAE;QAAU;IACnD;IAEA,sDAAsD;IACtD,MAAM,yBAAyB,OAAO;QACpC,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,kBAAkB;QAClB,IAAI;YACF,+CAA+C;YAC/C,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,wBAAwB,QAAQ,OAAO,EAAE,QAAQ,KAAK;YAEzF,IAAI,CAAC,aAAa;gBAChB,QAAQ,KAAK,CAAC,oCAAoC,QAAQ,OAAO;gBACjE,qBAAqB;gBACrB,aAAa;gBACb;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,YAAY,EAAE,EAAE,aAAa,YAAY;YAEjF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,oBAAoB,CAAC,aAAa,aAAa;YAC1E,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,GAAG,CAAC,mBAAmB,SAAS,IAAI;gBAC5C,qBAAqB,SAAS,IAAI;gBAClC,sDAAsD;gBACtD,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;gBACzE,MAAM,gBAAgB,WAAW,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG,GAAG;gBAC1D,aAAa;YACf,OAAO;gBACL,QAAQ,GAAG,CAAC,0BAA0B,aAAa;gBACnD,qBAAqB;gBACrB,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,qBAAqB;YACrB,aAAa;QACf,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,0BAA0B,OAAO,QAAmB,UAAkB;QAC1E,IAAI,CAAC,QAAQ,OAAO;QAEpB,6BAA6B;QAC7B,IAAI,OAAO,cAAc,IAAI,WAAW,WAAW,OAAO,cAAc,CAAC,QAAQ,KAAK;YACpF,OAAO;QACT;QAEA,mFAAmF;QACnF,IAAI,OAAO,IAAI,KAAK,UAAU,OAAO,IAAI,KAAK,mBAAmB;YAC/D,IAAI;gBACF,mDAAmD;gBACnD,MAAM,iBAAiB,WAAW,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC7C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,WAAW,KAAK,SAAS;wBACzB,SAAS;4BACP,WAAW,KAAK,OAAO,EAAE;wBAC3B;oBACF,CAAC;gBAED,wDAAwD;gBACxD,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,0BAA0B,CAAC;oBACpD,eAAe,OAAO,IAAI;oBAC1B,YAAY;oBACZ,YAAY,kBAAkB;oBAC9B;oBACA;gBACF;gBAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,OAAO,SAAS,IAAI,CAAC,QAAQ,IAAI;gBACnC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oDAAoD;YAClE,uCAAuC;YACzC;QACF;QAEA,2DAA2D;QAC3D,IAAI,WAAW;QAEf,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,WAAW,WAAW,CAAC,WAAW,OAAO,KAAK,CAAC,QAAQ,MAAM,GAAG;gBAChE;YACF,KAAK;gBACH,WAAW,WAAW,OAAO,KAAK,CAAC,QAAQ;gBAC3C;YACF,KAAK;gBACH,WAAW;gBACX;YACF,KAAK;gBACH,mDAAmD;gBACnD,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;gBAE5E,2DAA2D;gBAC3D,MAAM,SAAS;gBACf,MAAM,SAAS;gBACf,MAAM,YAAY,KAAK,KAAK,CAAC,gBAAgB,UAAU;gBAEvD,IAAI,YAAY,GAAG;oBACjB,0DAA0D;oBAC1D,MAAM,cAAc;2BAAI;qBAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;oBAC5E,IAAI,mBAAmB;oBAEvB,KAAK,MAAM,QAAQ,YAAa;wBAC9B,IAAI,oBAAoB,GAAG;wBAC3B,MAAM,iBAAiB,KAAK,GAAG,CAAC,kBAAkB,KAAK,QAAQ;wBAC/D,YAAY,iBAAiB,KAAK,SAAS;wBAC3C,oBAAoB;oBACtB;gBACF;gBACA;YACF,KAAK;gBACH,iCAAiC;gBACjC,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;gBACvE,IAAI,YAAY,IAAI;oBAClB,WAAW,WAAW,KAAK,oBAAoB;gBACjD,OAAO,IAAI,YAAY,GAAG;oBACxB,WAAW,WAAW,MAAM,kBAAkB;gBAChD;gBACA;YACF;gBACE,WAAW;QACf;QAEA,sCAAsC;QACtC,IAAI,OAAO,WAAW,EAAE;YACtB,WAAW,KAAK,GAAG,CAAC,UAAU,WAAW,OAAO,WAAW,CAAC,QAAQ;QACtE;QAEA,wBAAwB;QACxB,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,QAAQ;YACV,cAAc,OAAO,IAAI;YACzB,iBAAiB;YACjB,gBAAgB;YAEhB,+BAA+B;YAC/B,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;YACzE,MAAM,qBAAqB,MAAM,wBAAwB,QAAQ,UAAU;YAC3E,kBAAkB;YAElB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,sBAAsB,EAAE,mBAAmB,OAAO,CAAC,IAAI;QAC9F,OAAO;YACL,cAAc;YACd,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;QACpB;QACA,oBAAoB;IACtB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,YAAY;QACjB,gBAAgB;QAChB,iBAAiB;QACjB,IAAI;YACF,MAAM,MAAM,MAAM,iHAAA,CAAA,MAAG,CAAC,cAAc,CAAC;YACrC,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE;gBAC3B,mBAAmB,IAAI,IAAI;YAC7B,OAAO;gBACL,gBAAgB;gBAChB,iBAAiB;gBACjB,kBAAkB;gBAClB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,GAAQ;YACf,gBAAgB;YAChB,iBAAiB;YACjB,kBAAkB;YAClB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI;QAC3B;IACF;IAEA,mBAAmB;IACnB,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;IAEzE,mEAAmE;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI,iBAAiB,WAAW,GAAG;gBACjC,MAAM,cAAc,MAAM,wBAAwB,eAAe,UAAU;gBAC3E,kBAAkB;YACpB;QACF;QAEA;IACF,GAAG;QAAC;QAAU;QAAgB;KAAc;IAE5C,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB,WAAW,GAAG;YACrC,MAAM,gBAAgB,WAAW,CAAC,kBAAkB,IAAI,GAAG,GAAG;YAC9D,aAAa;QACf;IACF,GAAG;QAAC;QAAU;KAAkB;IAEhC,MAAM,cAAc,WAAW,iBAAiB,iBAAiB;IAEjE,MAAM,eAAe;QACnB,IAAI,CAAC,kBAAkB;YACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,0BAA0B,CAAC,yBAAyB;YACvD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,iDAAiD;QACjD,KAAK,MAAM,QAAQ,WAAY;YAC7B,IAAI,CAAC,KAAK,OAAO,EAAE,WAAW;gBAC5B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,iBAAiB,KAAK,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,MACzD,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,QAAQ,GAAG,IAAI,WAAW,GAAG;YAErD,IAAI,KAAK,QAAQ,GAAG,gBAAgB;gBAClC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,+BAA+B,EAAE,KAAK,OAAO,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE;gBACjG;YACF;QACF;QAEA,IAAI;YACF,WAAW;YAEX,MAAM,YAAY;gBAChB,YAAY,iBAAiB,EAAE;gBAC/B,kBAAkB,uBAAuB,EAAE;gBAC3C,mBAAmB,wBAAwB,EAAE;gBAC7C,OAAO,WAAW,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC7B,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,WAAW,KAAK,SAAS,CAAC,OAAO,CAAC;oBACpC,CAAC;gBACD,gBAAgB,eAAe,OAAO,CAAC;gBACvC,gBAAgB,eAAe,OAAO,CAAC;gBACvC,WAAW,UAAU,OAAO,CAAC;gBAC7B,YAAY,gBAAgB,cAAc,IAAI,GAAG;YACnD;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,WAAW,CAAC;YAEvC,IAAI,SAAS,OAAO,EAAE;gBACpB,uBAAuB;gBACvB,IAAI,MAAM,IAAI,MAAM,SAAS,IAAI,EAAE;oBACjC,MAAM,iHAAA,CAAA,MAAG,CAAC,YAAY,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI;gBACrD;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,+CAA+C;YAC/C,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;gBAChC,MAAM,mBAAmB,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;gBACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,mBAAmB,EAAE,iBAAiB,GAAG,CAAC,CAAC,IAAW,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO;YACxF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,WAAW;YAChC;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,QAAQ;QACR,oBAAoB;QACpB,0BAA0B;QAC1B,2BAA2B;QAC3B,cAAc,EAAE;QAChB,kBAAkB;QAClB,kBAAkB;QAClB,aAAa;QACb,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,iBAAiB;QACjB,oBAAoB;QACpB,qBAAqB;QACrB,kBAAkB;IACpB;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ;YACA,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACvC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,MAAM,oBAAoB,oBAAoB,0BAA0B;IACxE,MAAM,oBAAoB,WAAW,MAAM,GAAG;IAE9C,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO,KAAK,QAAQ;oBAAI,eAAe,CAAC,QAAU,QAAQ,SAAS;;sCACvE,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAI,WAAU;;sDAC/B,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAI,UAAU,CAAC;oCAAmB,WAAU;;sDAC7D,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGjC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAI,UAAU,CAAC;oCAAmB,WAAU;;sDAC7D,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAKtC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAI,WAAU;sCAC/B,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,kBAAkB,MAAM;gEAAI,eAAe;;kFACxD,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,8OAAC,kIAAA,CAAA,gBAAa;kFACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;gFAAmB,OAAO,SAAS,EAAE;;oFAC7C,SAAS,SAAS;oFAAC;oFAAE,SAAS,QAAQ;oFAAC;oFAAI,SAAS,KAAK;;+EAD3C,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;oDAQnC,kCACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAiB;;;;;;kFAChC,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,wBAAwB,MAAM;wEACrC,eAAe,CAAC;4EACd,MAAM,UAAU,iBAAiB,SAAS,EAAE,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;4EAC/D,0BAA0B,WAAW;wEACvC;;0FAEA,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;0FACX,iBAAiB,SAAS,EAAE,IAAI,CAAC,wBAChC,8OAAC,kIAAA,CAAA,aAAU;wFAAkB,OAAO,QAAQ,EAAE;;4FAC3C,QAAQ,SAAS;4FAAC;4FAAE,QAAQ,QAAQ;4FAAC;4FAAI,QAAQ,QAAQ;4FAAC;4FAAG,QAAQ,IAAI;;uFAD3D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;0EAQnC,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAkB;;;;;;kFACjC,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,yBAAyB,MAAM;wEACtC,eAAe,CAAC;4EACd,MAAM,UAAU,iBAAiB,SAAS,EAAE,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;4EAC/D,2BAA2B,WAAW;4EACtC,0DAA0D;4EAC1D,IAAI,SAAS;gFACX,uBAAuB;4EACzB,OAAO;gFACL,qBAAqB;gFACrB,aAAa;4EACf;wEACF;;0FAEA,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;0FACX,iBAAiB,SAAS,EAAE,IAAI,CAAC,wBAChC,8OAAC,kIAAA,CAAA,aAAU;wFAAkB,OAAO,QAAQ,EAAE;;4FAC3C,QAAQ,SAAS;4FAAC;4FAAE,QAAQ,QAAQ;4FAAC;4FAAI,QAAQ,QAAQ;4FAAC;4FAAG,QAAQ,IAAI;;uFAD3D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAWzC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,QAAQ;oDACvB,UAAU,CAAC;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAI,WAAU;sCAC/B,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;wDAAqB,WAAU;;0EAC9B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAe,QAAQ,IAAI;;;;;;0FACzC,8OAAC;gFAAE,WAAU;0FAAiC,QAAQ,WAAW;;;;;;;;;;;;kFAEnE,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;;4EAAW,QAAQ,QAAQ,EAAE,UAAU;4EAAE;;;;;;;;;;;;;4DAGzD,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC;oEACrB,gBAAgB;oEAChB,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,IAAI,EAAE,cAAc,QAAQ,SAAS;oEAE/E,MAAM,iBAAiB,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK;wEACrD,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC;wEACzE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,UAAU,CAAC,WAAW,EAAE,IAAI,QAAQ,CAAC,cAAc,EAAE,IAAI,WAAW,CAAC,YAAY,EAAE,WAAW;wEAC1H,OAAO,MAAM;oEACf,GAAG,MAAM;oEAET,QAAQ,GAAG,CAAC,gCAAgC;oEAE5C,yCAAyC;oEACzC,MAAM,eAAe,kBAAkB,gBAAgB,QAAQ,aAAa,EAAE,KAC5E,CAAA,KAAM,GAAG,YAAY,KAAK,iBAAiB,YAAY;oEAGzD,MAAM,eAAe,eAChB,aAAa,SAAS,IAAI,aAAa,YAAY,GACnD,QAAQ,SAAS,IAAI,QAAQ,YAAY;oEAE9C,MAAM,YAAY,eACd,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE,iBAAiB,YAAY,CAAC,OAAO,CAAC,GAC3D,CAAC,CAAC,EAAE,cAAc;oEAEtB,qBACE,8OAAC;wEAAqB,WAAU;;0FAC9B,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGAAe,QAAQ,IAAI;;;;;;kGAC1C,8OAAC;wFAAI,WAAU;;4FAAgC;4FACvC,QAAQ,GAAG;4FAAC;4FAAI;0GACtB,8OAAC;gGAAK,WAAU;0GACb,iBAAiB,kBAChB,8OAAC,iIAAA,CAAA,QAAK;oGAAC,SAAQ;oGAAU,WAAU;;wGAChC;wGAAe;;;;;;yHAGlB,8OAAC,iIAAA,CAAA,QAAK;oGAAC,SAAQ;oGAAc,WAAU;8GAAO;;;;;;;;;;;;;;;;;;;;;;;0FAOtD,8OAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAS,IAAM,mBAAmB;gFAClC,UAAU,kBAAkB,KAAK,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK,QAAQ,EAAE;;kGAEtF,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;uEAvB3B,QAAQ,EAAE;;;;;gEA4BxB;;;;;;;uDAjEI,QAAQ,EAAE;;;;;;;;;;0DAwExB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,SAAS,IAAM,QAAQ;kEAAI;;;;;;kEAGrD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,IAAM,QAAQ;wDACvB,UAAU,CAAC;kEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAI,WAAU;sCAC/B,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAc;;;;;;oDAC3B,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;4DAAyB,WAAU;;8EAClC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;gFAAe,KAAK,OAAO,EAAE,SAAS;gFAAK;gFAAI,KAAK,OAAO,EAAE;;;;;;;sFAC5E,8OAAC;4EAAI,WAAU;;gFAAgC;gFAAM,KAAK,OAAO,EAAE;;;;;;;;;;;;;8EAErE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,OAAO,KAAK,QAAQ;4EACpB,UAAU,CAAC,IAAM,qBAAqB,KAAK,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4EAClF,WAAU;4EACV,KAAI;;;;;;sFAEN,8OAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,8OAAC,iIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,OAAO,KAAK,SAAS;4EACrB,UAAU,CAAC,IAAM,kBAAkB,KAAK,SAAS,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EACjF,WAAU;4EACV,KAAI;4EACJ,MAAK;;;;;;sFAEP,8OAAC;4EAAK,WAAU;;gFAAsB;gFAAE,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;sFACvE,8OAAC,kIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,sBAAsB,KAAK,SAAS;sFAEnD,cAAA,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;2DA5Bd,KAAK,SAAS;;;;;;;;;;;0DAoC5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAK;4EAAE,SAAS,OAAO,CAAC;;;;;;;;;;;;;0EAE3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO;wEACP,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wEACjE,WAAU;wEACV,KAAI;wEACJ,MAAK;;;;;;;;;;;;0EAGT,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO;wEACP,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wEACjE,WAAU;wEACV,KAAI;wEACJ,MAAK;;;;;;;;;;;;0EAGT,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;8FAAK;;;;;;gFACL,mCACC,8OAAC;oFAAK,WAAU;;wFACb,kBAAkB,OAAO;wFAAE,kBAAkB,KAAK,GAAG,CAAC,EAAE,EAAE,kBAAkB,KAAK,EAAE,GAAG;wFAAG;wFAAI,kBAAkB,IAAI;wFAAC;wFAAI,kBAAkB,IAAI;wFAAC;;;;;;;gFAGnJ,gCACC,8OAAC;oFAAK,WAAU;8FAAgC;;;;;;gFAEjD,CAAC,qBAAqB,CAAC,kBAAkB,yCACxC,8OAAC;oFAAK,WAAU;8FAAgC;;;;;;;;;;;;sFAGpD,8OAAC,iIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EAC5D,WAAU;4EACV,KAAI;4EACJ,MAAK;4EACL,aAAY;;;;;;;;;;;;;;;;;0EAIlB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAK;4EAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0DAMlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAI5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,mIAAA,CAAA,UAAO;gEAAC,MAAM;gEAAkB,cAAc;;kFAC7C,8OAAC,mIAAA,CAAA,iBAAc;wEAAC,OAAO;kFACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,iBAAe;4EACf,WAAU;;gFAET,gBAAgB,cAAc,IAAI,GAAG;8FACtC,8OAAC,oNAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;kFAG3B,8OAAC,mIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACxB,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8FACN,8OAAC,mIAAA,CAAA,eAAY;oFAAC,aAAY;;;;;;8FAC1B,8OAAC,mIAAA,CAAA,eAAY;8FAAC;;;;;;8FACd,8OAAC,mIAAA,CAAA,eAAY;8FACV,iBAAiB,GAAG,CAAC,CAAC;wFACrB,sDAAsD;wFACtD,IAAI,WAAW;wFACf,MAAM,aAAa,CAAC,OAAO,cAAc,IAAI,YAAY,WAAW,OAAO,cAAc,CAAC,QAAQ;wFAElG,IAAI,YAAY;4FACd,OAAQ,OAAO,IAAI;gGACjB,KAAK;oGACH,WAAW,WAAW,CAAC,WAAW,OAAO,KAAK,CAAC,QAAQ,MAAM,GAAG;oGAChE;gGACF,KAAK;oGACH,WAAW,WAAW,OAAO,KAAK,CAAC,QAAQ;oGAC3C;gGACF,KAAK;oGACH,WAAW;oGACX;gGACF,KAAK;oGACH,WAAW,WAAW,MAAM,iCAAiC;oGAC7D;gGACF,KAAK;oGACH,WAAW,WAAW,KAAK,iCAAiC;oGAC5D;4FACJ;4FAEA,IAAI,OAAO,WAAW,EAAE;gGACtB,WAAW,KAAK,GAAG,CAAC,UAAU,WAAW,OAAO,WAAW,CAAC,QAAQ;4FACtE;4FACA,WAAW,KAAK,GAAG,CAAC,UAAU;wFAChC;wFAEA,qBACE,8OAAC,mIAAA,CAAA,cAAW;4FAEV,OAAO,OAAO,IAAI;4FAClB,UAAU,IAAM,mBAAmB;4FACnC,UAAU,CAAC;4FACX,WAAW,CAAC,aAAa,eAAe;;8GAExC,8OAAC,oMAAA,CAAA,QAAK;oGACJ,WAAW,CAAC,aAAa,EACvB,eAAe,OAAO,OAAO,EAAE,GAAG,gBAAgB,aAClD;;;;;;8GAEJ,8OAAC;oGAAI,WAAU;;sHACb,8OAAC;4GAAI,WAAU;;8HACb,8OAAC;oHAAK,WAAU;8HAAe,OAAO,IAAI;;;;;;8HAC1C,8OAAC;oHAAK,WAAU;8HACb,aACC,OAAO,IAAI,KAAK,uBACd,8OAAC;wHAAI,WAAU;;0IACb,8OAAC;0IAAK,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,IAAI;;;;;;0IAChC,8OAAC;gIAAI,WAAU;0IACZ,CAAC;oIACA,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;oIACvE,MAAM,SAAS,AAAC,OAAe,WAAW,IAAI;oIAC9C,MAAM,SAAS,AAAC,OAAe,WAAW,IAAI;oIAC9C,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW,UAAU;oIAClD,OAAO,GAAG,UAAU,UAAU,EAAE,cAAc,IAAI,MAAM,IAAI;gIAC9D,CAAC;;;;;;;;;;;+HAGH,OAAO,IAAI,KAAK,oBAClB,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,IAAI,GAE1B,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,IAAI,GAE1B;;;;;;;;;;;;sHAGR,8OAAC;4GAAI,WAAU;;gHACZ,OAAO,IAAI;gHACX,OAAO,cAAc,kBACpB,8OAAC;oHAAK,WAAU;;wHAAO;wHACb,WAAW,OAAO,cAAc,CAAC,QAAQ,IAAI,OAAO,CAAC;wHAAG;;;;;;;;;;;;;;;;;;;;2FAzCnE,OAAO,EAAE;;;;;oFAgDpB;;;;;;;;;;;;;;;;;;;;;;;0EAMR,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,KAAK;oEACL,OAAO;oEACP,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;oEAC3C,aAAY;oEACZ,WAAU;;;;;;;;;;;0EAId,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS;gEACT,UAAU,iBAAiB,cAAc,CAAC;0EAEzC,iBAAiB,aAAa,gBAAgB;;;;;;4DAGhD,+BACC,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,mBAAmB;gEAClC,MAAK;0EACN;;;;;;;;;;;;oDAMJ,iBAAiB,WAAW,+BAC3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEAAc;oEAAmB,cAAc,IAAI;;;;;;;0EAClE,8OAAC;0EAAK,cAAc,IAAI;;;;;;0EACxB,8OAAC;;oEAAI;oEAAY,eAAe,OAAO,CAAC;;;;;;;4DAEvC,cAAc,IAAI,KAAK,wBACtB,8OAAC;gEAAI,WAAU;0EACZ,CAAC;oEACA,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;oEACvE,MAAM,SAAS,AAAC,cAAsB,WAAW,IAAI;oEACrD,MAAM,SAAS,AAAC,cAAsB,WAAW,IAAI;oEACrD,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW,UAAU;oEAClD,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,UAAU;oEAElD,qBACE,8OAAC;;0FACC,8OAAC;;oFAAI;oFAAI;oFAAU;oFAAW,cAAc,IAAI,MAAM;oFAAG;;;;;;;4EACxD,cAAc,mBACb,8OAAC;gFAAI,WAAU;;oFAAkB;oFAC1B,SAAS;oFAAS;oFAAW,SAAS,aAAa,IAAI,MAAM;oFAAG;oFAAS;oFAAO;;;;;;;4EAGxF,YAAY,KAAK,WAAW,4BAC3B,8OAAC;gFAAI,WAAU;;oFAAgB;oFACxB,aAAa;oFAAS;;;;;;;;;;;;;gEAKrC,CAAC;;;;;;4DAIJ,cAAc,cAAc,IAAI,WAAW,WAAW,cAAc,cAAc,CAAC,QAAQ,qBAC1F,8OAAC;gEAAI,WAAU;;oEAAkB;oEACzB,CAAC,WAAW,cAAc,cAAc,CAAC,QAAQ,MAAM,QAAQ,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;oDAMzF,iBAAiB,2BAChB,8OAAC;wDAAI,WAAU;kEAA6C;;;;;;;;;;;;0DAMhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,SAAS,IAAM,QAAQ;kEAAI;;;;;;kEAGrD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAS;wDAAc,UAAU;kEACtC,UAAU,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 6070, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/orders/edit-order-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Package, CreditCard, FileText, Truck } from 'lucide-react';\r\nimport { api, Order } from '@/lib/api';\r\nimport { toast } from 'sonner';\r\nimport { Dialog as UIDialog, DialogContent as UIDialogContent, DialogHeader as UIDialogHeader, DialogTitle as UIDialogTitle } from '@/components/ui/dialog';\r\n\r\ninterface EditOrderDialogProps {\r\n  order: Order | null;\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onSuccess: () => void;\r\n}\r\n\r\nexport function EditOrderDialog({ order, open, onOpenChange, onSuccess }: EditOrderDialogProps) {\r\n  const [loading, setLoading] = useState(false);\r\n  const [status, setStatus] = useState('');\r\n  const [discountAmount, setDiscountAmount] = useState(0);\r\n  const [shippingAmount, setShippingAmount] = useState(0);\r\n  const [taxAmount, setTaxAmount] = useState(0);\r\n  const [notes, setNotes] = useState('');\r\n  const [showRefundDialog, setShowRefundDialog] = useState(false);\r\n  const [refundAmount, setRefundAmount] = useState(0);\r\n  const [refundReason, setRefundReason] = useState('');\r\n  const [refundLoading, setRefundLoading] = useState(false);\r\n  const [refundError, setRefundError] = useState('');\r\n  const [refundStatusLoading, setRefundStatusLoading] = useState<string | null>(null);\r\n\r\n  // Shipping state\r\n  const [shipments, setShipments] = useState<any[]>([]);\r\n  const [shipmentsLoading, setShipmentsLoading] = useState(false);\r\n  const [showCreateShipment, setShowCreateShipment] = useState(false);\r\n  const [newShipment, setNewShipment] = useState({\r\n    carrier: '',\r\n    trackingNumber: '',\r\n    trackingUrl: '',\r\n    status: 'PENDING'\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (order) {\r\n      setStatus(order.status);\r\n      setDiscountAmount(order.discountAmount);\r\n      setShippingAmount(order.shippingAmount);\r\n      setTaxAmount(order.taxAmount);\r\n      setNotes('');\r\n      fetchShipments();\r\n    }\r\n  }, [order]);\r\n\r\n  const fetchShipments = async () => {\r\n    if (!order) return;\r\n\r\n    setShipmentsLoading(true);\r\n    try {\r\n      const response = await api.getOrderShipments(order.id);\r\n      if (response.success) {\r\n        setShipments(response.data || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to fetch shipments:', error);\r\n    } finally {\r\n      setShipmentsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCreateShipment = async () => {\r\n    if (!order) return;\r\n\r\n    try {\r\n      const response = await api.createShipment({\r\n        orderId: order.id,\r\n        ...newShipment\r\n      });\r\n\r\n      if (response.success) {\r\n        toast.success('Shipment created successfully');\r\n        setShowCreateShipment(false);\r\n        setNewShipment({\r\n          carrier: '',\r\n          trackingNumber: '',\r\n          trackingUrl: '',\r\n          status: 'PENDING'\r\n        });\r\n        fetchShipments();\r\n        onSuccess();\r\n      } else {\r\n        toast.error('Failed to create shipment');\r\n      }\r\n    } catch (error) {\r\n      toast.error('Failed to create shipment');\r\n    }\r\n  };\r\n\r\n  const handleUpdateShipment = async (shipmentId: string, data: any) => {\r\n    try {\r\n      const response = await api.updateShipmentTracking(shipmentId, data);\r\n\r\n      if (response.success) {\r\n        toast.success('Shipment updated successfully');\r\n        fetchShipments();\r\n        onSuccess();\r\n      } else {\r\n        toast.error('Failed to update shipment');\r\n      }\r\n    } catch (error) {\r\n      toast.error('Failed to update shipment');\r\n    }\r\n  };\r\n\r\n  const handleDeleteShipment = async (shipmentId: string) => {\r\n    try {\r\n      const response = await api.deleteShipment(shipmentId);\r\n\r\n      if (response.success) {\r\n        toast.success('Shipment deleted successfully');\r\n        fetchShipments();\r\n        onSuccess();\r\n      } else {\r\n        toast.error('Failed to delete shipment');\r\n      }\r\n    } catch (error) {\r\n      toast.error('Failed to delete shipment');\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!order) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      \r\n      const updateData = {\r\n        status,\r\n        discountAmount: discountAmount.toString(),\r\n        shippingAmount: shippingAmount.toString(),\r\n        taxAmount: taxAmount.toString(),\r\n      };\r\n\r\n      const response = await api.updateOrder(order.id, updateData);\r\n      \r\n      if (response.success) {\r\n        // Add note if provided\r\n        if (notes.trim()) {\r\n          await api.addOrderNote(order.id, notes.trim());\r\n        }\r\n        \r\n        onSuccess();\r\n        onOpenChange(false);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update order:', error);\r\n      toast.error('Failed to update order');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleInitiateRefund = async () => {\r\n    if (!order || !order.payments || order.payments.length === 0) return;\r\n    setRefundLoading(true);\r\n    setRefundError('');\r\n    try {\r\n      const payment = order.payments[0];\r\n      const response = await api.initiateOrderRefund(order.id, refundAmount, refundReason);\r\n      if (response.success) {\r\n        setShowRefundDialog(false);\r\n        setRefundAmount(0);\r\n        setRefundReason('');\r\n        onSuccess();\r\n        toast.success('Refund initiated');\r\n      } else {\r\n        setRefundError(response.error || 'Failed to initiate refund');\r\n      }\r\n    } catch (e) {\r\n      setRefundError('Failed to initiate refund');\r\n    } finally {\r\n      setRefundLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleUpdateRefundStatus = async (refundId: string, newStatus: string) => {\r\n    setRefundStatusLoading(refundId);\r\n    try {\r\n      const response = await api.updateRefundStatus(refundId, newStatus);\r\n      if (response.success) {\r\n        onSuccess();\r\n        toast.success('Refund status updated');\r\n      } else {\r\n        toast.error(response.error || 'Failed to update refund status');\r\n      }\r\n    } catch (e) {\r\n      toast.error('Failed to update refund status');\r\n    } finally {\r\n      setRefundStatusLoading(null);\r\n    }\r\n  };\r\n\r\n  if (!order) return null;\r\n\r\n  const subtotal = order.subtotal || 0;\r\n  const totalAmount = Number(subtotal ?? 0) - Number(discountAmount ?? 0) + Number(shippingAmount ?? 0) + Number(taxAmount ?? 0);\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle>Edit Order #{order.orderNumber}</DialogTitle>\r\n          <DialogDescription>\r\n            Update order details and status\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <Tabs defaultValue=\"details\" className=\"w-full\">\r\n          <TabsList className=\"grid w-full grid-cols-4\">\r\n            <TabsTrigger value=\"details\">Order Details</TabsTrigger>\r\n            <TabsTrigger value=\"items\">Items</TabsTrigger>\r\n            <TabsTrigger value=\"payments\">Payments</TabsTrigger>\r\n            <TabsTrigger value=\"shipping\">Shipping</TabsTrigger>\r\n            <TabsTrigger value=\"audit\">Audit Trail</TabsTrigger>\r\n            <TabsTrigger value=\"refunds\">Refunds</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"details\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>Order Information</CardTitle>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                <div className=\"grid gap-4 md:grid-cols-2\">\r\n                  <div>\r\n                    <Label htmlFor=\"status\">Order Status</Label>\r\n                    <Select value={status} onValueChange={setStatus}>\r\n                      <SelectTrigger>\r\n                        <SelectValue placeholder=\"Select status\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        <SelectItem value=\"PENDING\">Pending</SelectItem>\r\n                        <SelectItem value=\"PROCESSING\">Processing</SelectItem>\r\n                        <SelectItem value=\"SHIPPED\">Shipped</SelectItem>\r\n                        <SelectItem value=\"DELIVERED\">Delivered</SelectItem>\r\n                        <SelectItem value=\"CANCELLED\">Cancelled</SelectItem>\r\n                        <SelectItem value=\"REFUNDED\">Refunded</SelectItem>\r\n                        <SelectItem value=\"ON_HOLD\">On Hold</SelectItem>\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <Label>Customer</Label>\r\n                    <div className=\"p-2 bg-gray-50 rounded text-black\">\r\n                      {order.customer ? \r\n                        `${order.customer.firstName} ${order.customer.lastName}` : \r\n                        'Guest Customer'\r\n                      }\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <Label>Order Summary</Label>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Subtotal:</span>\r\n                      <span>${Number(subtotal ?? 0).toFixed(2)}</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span>Discount:</span>\r\n                      <Input\r\n                        type=\"number\"\r\n                        value={discountAmount}\r\n                        onChange={(e) => setDiscountAmount(parseFloat(e.target.value) || 0)}\r\n                        className=\"w-24 text-right\"\r\n                        min=\"0\"\r\n                        step=\"0.01\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span>Shipping:</span>\r\n                      <Input\r\n                        type=\"number\"\r\n                        value={shippingAmount}\r\n                        onChange={(e) => setShippingAmount(parseFloat(e.target.value) || 0)}\r\n                        className=\"w-24 text-right\"\r\n                        min=\"0\"\r\n                        step=\"0.01\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span>Tax:</span>\r\n                      <Input\r\n                        type=\"number\"\r\n                        value={taxAmount}\r\n                        onChange={(e) => setTaxAmount(parseFloat(e.target.value) || 0)}\r\n                        className=\"w-24 text-right\"\r\n                        min=\"0\"\r\n                        step=\"0.01\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-between font-medium text-lg pt-2 border-t\">\r\n                      <span>Total:</span>\r\n                      <span>${Number(totalAmount ?? 0).toFixed(2)}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"notes\">Add Note (Optional)</Label>\r\n                  <Textarea\r\n                    id=\"notes\"\r\n                    placeholder=\"Add a note about this order update...\"\r\n                    value={notes}\r\n                    onChange={(e) => setNotes(e.target.value)}\r\n                  />\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"items\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Package className=\"w-5 h-5\" />\r\n                  Order Items\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-3\">\r\n                  {order.items && order.items.length > 0 ? (\r\n                    order.items.map((item) => (\r\n                      <div key={item.id} className=\"flex items-center justify-between p-3 border rounded\">\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"font-medium\">\r\n                            {item.variant?.product?.name || 'Unknown Product'} - {item.variant?.name || 'Unknown Variant'}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">\r\n                            SKU: {item.variant?.sku || 'N/A'}\r\n                            {order.customer?.customerType && item.variant?.segmentPrices && item.variant.segmentPrices.length > 0 && (\r\n                              <span className=\"ml-2\">\r\n                                (Customer Segment: {order.customer.customerType})\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                          {item.variant && item.variant.segmentPrices && item.variant.segmentPrices.length > 0 && (\r\n                            <div className=\"text-xs text-muted-foreground mt-1\">\r\n                              Segment Prices:\r\n                              {item.variant.segmentPrices.map(sp => (\r\n                                <span key={sp.id} className=\"ml-2\">\r\n                                  {sp.customerType}: ${sp.salePrice || sp.regularPrice}\r\n                                </span>\r\n                              ))}\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <span className=\"text-sm\">Qty: {item.quantity}</span>\r\n                          <span className=\"text-sm\">Unit: ${Number(item.unitPrice).toFixed(2)}</span>\r\n                          <span className=\"text-sm font-medium\">${Number(item.totalPrice).toFixed(2)}</span>\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                  ) : (\r\n                    <p className=\"text-muted-foreground text-center py-4\">No items found</p>\r\n                  )}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"payments\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <CreditCard className=\"w-5 h-5\" />\r\n                  Payment History\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-3\">\r\n                  {order.payments && order.payments.length > 0 ? (\r\n                    order.payments.map((payment) => (\r\n                      <div key={payment.id} className=\"flex items-center justify-between p-3 border rounded\">\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"font-medium\">\r\n                            {payment.paymentMethod}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">\r\n                            {payment.paidAt ? new Date(payment.paidAt).toLocaleDateString() : 'Not paid'}\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <Badge variant={payment.status === 'PAID' ? 'default' : 'secondary'}>\r\n                            {payment.status}\r\n                          </Badge>\r\n                          <span className=\"text-sm font-medium\">${Number(payment.amount ?? 0).toFixed(2)}</span>\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                  ) : (\r\n                    <p className=\"text-muted-foreground text-center py-4\">No payments found</p>\r\n                  )}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"shipping\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Truck className=\"w-5 h-5\" />\r\n                  Shipping Information\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"grid gap-4 md:grid-cols-2\">\r\n                    <div>\r\n                      <Label>Billing Address</Label>\r\n                      <div className=\"p-2 bg-gray-50 rounded text-sm text-black\">\r\n                        {order.billingAddress ? (\r\n                          <div>\r\n                            <div>{order.billingAddress.firstName} {order.billingAddress.lastName}</div>\r\n                            <div>{order.billingAddress.address1}</div>\r\n                            {order.billingAddress.address2 && <div>{order.billingAddress.address2}</div>}\r\n                            <div>{order.billingAddress.city}, {order.billingAddress.state} {order.billingAddress.postalCode}</div>\r\n                            <div>{order.billingAddress.country}</div>\r\n                          </div>\r\n                        ) : (\r\n                          'No billing address'\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div>\r\n                      <Label>Shipping Address</Label>\r\n                      <div className=\"p-2 bg-gray-50 rounded text-sm text-black\">\r\n                        {order.shippingAddress ? (\r\n                          <div>\r\n                            <div>{order.shippingAddress.firstName} {order.shippingAddress.lastName}</div>\r\n                            <div>{order.shippingAddress.address1}</div>\r\n                            {order.shippingAddress.address2 && <div>{order.shippingAddress.address2}</div>}\r\n                            <div>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}</div>\r\n                            <div>{order.shippingAddress.country}</div>\r\n                          </div>\r\n                        ) : (\r\n                          'No shipping address'\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <Label>Shipments</Label>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => setShowCreateShipment(true)}\r\n                      >\r\n                        Create Shipment\r\n                      </Button>\r\n                    </div>\r\n\r\n                    {shipmentsLoading ? (\r\n                      <div className=\"text-center py-4 text-muted-foreground\">\r\n                        Loading shipments...\r\n                      </div>\r\n                    ) : shipments.length > 0 ? (\r\n                      shipments.map((shipment) => (\r\n                        <div key={shipment.id} className=\"p-4 border rounded space-y-3\">\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-medium\">{shipment.carrier}</div>\r\n                              <div className=\"text-sm text-muted-foreground\">\r\n                                Tracking: {shipment.trackingNumber || 'N/A'}\r\n                              </div>\r\n                              {shipment.trackingUrl && (\r\n                                <a\r\n                                  href={shipment.trackingUrl}\r\n                                  target=\"_blank\"\r\n                                  rel=\"noopener noreferrer\"\r\n                                  className=\"text-sm text-blue-600 hover:underline\"\r\n                                >\r\n                                  Track Package\r\n                                </a>\r\n                              )}\r\n                            </div>\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <Badge variant={shipment.status === 'DELIVERED' ? 'default' : 'outline'}>\r\n                                {shipment.status}\r\n                              </Badge>\r\n                              <Button\r\n                                variant=\"outline\"\r\n                                size=\"sm\"\r\n                                onClick={() => handleDeleteShipment(shipment.id)}\r\n                              >\r\n                                Delete\r\n                              </Button>\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"grid grid-cols-2 gap-4\">\r\n                            <div>\r\n                              <Label className=\"text-xs\">Tracking Number</Label>\r\n                              <Input\r\n                                value={shipment.trackingNumber || ''}\r\n                                onChange={(e) => handleUpdateShipment(shipment.id, { trackingNumber: e.target.value })}\r\n                                placeholder=\"Enter tracking number\"\r\n                                className=\"mt-1\"\r\n                              />\r\n                            </div>\r\n                            <div>\r\n                              <Label className=\"text-xs\">Tracking URL</Label>\r\n                              <Input\r\n                                value={shipment.trackingUrl || ''}\r\n                                onChange={(e) => handleUpdateShipment(shipment.id, { trackingUrl: e.target.value })}\r\n                                placeholder=\"Enter tracking URL\"\r\n                                className=\"mt-1\"\r\n                              />\r\n                            </div>\r\n                            <div>\r\n                              <Label className=\"text-xs\">Status</Label>\r\n                              <Select\r\n                                value={shipment.status}\r\n                                onValueChange={(value) => handleUpdateShipment(shipment.id, { status: value })}\r\n                              >\r\n                                <SelectTrigger className=\"mt-1\">\r\n                                  <SelectValue />\r\n                                </SelectTrigger>\r\n                                <SelectContent>\r\n                                  <SelectItem value=\"PENDING\">Pending</SelectItem>\r\n                                  <SelectItem value=\"SHIPPED\">Shipped</SelectItem>\r\n                                  <SelectItem value=\"IN_TRANSIT\">In Transit</SelectItem>\r\n                                  <SelectItem value=\"DELIVERED\">Delivered</SelectItem>\r\n                                  <SelectItem value=\"RETURNED\">Returned</SelectItem>\r\n                                  <SelectItem value=\"CANCELLED\">Cancelled</SelectItem>\r\n                                </SelectContent>\r\n                              </Select>\r\n                            </div>\r\n                            <div>\r\n                              <Label className=\"text-xs\">Carrier</Label>\r\n                              <Input\r\n                                value={shipment.carrier}\r\n                                onChange={(e) => handleUpdateShipment(shipment.id, { carrier: e.target.value })}\r\n                                placeholder=\"Enter carrier\"\r\n                                className=\"mt-1\"\r\n                              />\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"text-xs text-muted-foreground\">\r\n                            Created: {new Date(shipment.createdAt).toLocaleString()}\r\n                            {shipment.shippedAt && (\r\n                              <> • Shipped: {new Date(shipment.shippedAt).toLocaleString()}</>\r\n                            )}\r\n                            {shipment.deliveredAt && (\r\n                              <> • Delivered: {new Date(shipment.deliveredAt).toLocaleString()}</>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <p className=\"text-muted-foreground text-center py-4\">No shipments found</p>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"audit\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <FileText className=\"w-5 h-5\" />\r\n                  Audit Trail\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent>\r\n                {order.auditLogs && order.auditLogs.length > 0 ? (\r\n                  <div className=\"space-y-3\">\r\n                    {order.auditLogs.map((log) => (\r\n                      <div key={log.id} className=\"flex items-center gap-4 p-3 border rounded\">\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"font-medium\">\r\n                            {log.action.replace(/_/g, ' ')}\r\n                          </div>\r\n                          <div className=\"text-xs text-muted-foreground\">\r\n                            {log.user ? `${log.user.firstName} ${log.user.lastName} (${log.user.email})` : 'System'}\r\n                            {' • '}\r\n                            {new Date(log.createdAt).toLocaleString()}\r\n                          </div>\r\n                          <div className=\"text-xs text-muted-foreground mt-1\">\r\n                            {log.details && typeof log.details === 'object' ? (\r\n                              <pre className=\"whitespace-pre-wrap break-all bg-gray-50 p-2 rounded text-xs\">{JSON.stringify(log.details, null, 2)}</pre>\r\n                            ) : (\r\n                              <span>{log.details}</span>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ) : (\r\n                  <p className=\"text-muted-foreground text-center py-4\">No audit logs found</p>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"refunds\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <CreditCard className=\"w-5 h-5\" />\r\n                  Refunds\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent>\r\n                {order.payments && order.payments.some(p => p.refunds && p.refunds.length > 0) ? (\r\n                  <div className=\"space-y-3\">\r\n                    {order.payments.map(payment => (\r\n                      payment.refunds && payment.refunds.length > 0 && payment.refunds.map(refund => (\r\n                        <div key={refund.id} className=\"flex items-center gap-4 p-3 border rounded\">\r\n                          <div className=\"flex-1\">\r\n                            <div className=\"font-medium\">\r\n                              Refund: ${Number(refund.amount).toFixed(2)}\r\n                            </div>\r\n                            <div className=\"text-xs text-muted-foreground\">\r\n                              Status: {refund.status}\r\n                              {refund.refundedAt && (\r\n                                <>\r\n                                  {' • '}Refunded: {new Date(refund.refundedAt).toLocaleString()}\r\n                                </>\r\n                              )}\r\n                            </div>\r\n                            <div className=\"text-xs text-muted-foreground mt-1\">\r\n                              {refund.reason}\r\n                            </div>\r\n                          </div>\r\n                          {/* Admin: allow status update */}\r\n                          <div className=\"flex flex-col gap-2\">\r\n                            {['PENDING', 'APPROVED'].includes(refund.status) && (\r\n                              <>\r\n                                <Button size=\"sm\" variant=\"outline\" disabled={refundStatusLoading === refund.id} onClick={() => handleUpdateRefundStatus(refund.id, 'APPROVED')}>Approve</Button>\r\n                                <Button size=\"sm\" variant=\"outline\" disabled={refundStatusLoading === refund.id} onClick={() => handleUpdateRefundStatus(refund.id, 'PROCESSED')}>Mark as Processed</Button>\r\n                                <Button size=\"sm\" variant=\"destructive\" disabled={refundStatusLoading === refund.id} onClick={() => handleUpdateRefundStatus(refund.id, 'REJECTED')}>Reject</Button>\r\n                              </>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      ))\r\n                    ))}\r\n                  </div>\r\n                ) : (\r\n                  <p className=\"text-muted-foreground text-center py-4\">No refunds found</p>\r\n                )}\r\n                {/* Admin: Refund button */}\r\n                <div className=\"mt-4\">\r\n                  <Button variant=\"outline\" onClick={() => setShowRefundDialog(true)}>Initiate Refund</Button>\r\n                </div>\r\n                <UIDialog open={showRefundDialog} onOpenChange={setShowRefundDialog}>\r\n                  <UIDialogContent>\r\n                    <UIDialogHeader>\r\n                      <UIDialogTitle>Initiate Refund</UIDialogTitle>\r\n                    </UIDialogHeader>\r\n                    <div className=\"space-y-4\">\r\n                      <div>\r\n                        <Label>Amount</Label>\r\n                        <Input type=\"number\" min={0} max={order.totalAmount} value={refundAmount} onChange={e => setRefundAmount(Number(e.target.value))} />\r\n                      </div>\r\n                      <div>\r\n                        <Label>Reason</Label>\r\n                        <Textarea value={refundReason} onChange={e => setRefundReason(e.target.value)} />\r\n                      </div>\r\n                      {refundError && <div className=\"text-red-600 text-sm\">{refundError}</div>}\r\n                      <div className=\"flex justify-end gap-2\">\r\n                        <Button variant=\"outline\" onClick={() => setShowRefundDialog(false)}>Cancel</Button>\r\n                        <Button onClick={handleInitiateRefund} disabled={refundLoading || refundAmount <= 0}>{refundLoading ? 'Processing...' : 'Submit Refund'}</Button>\r\n                      </div>\r\n                    </div>\r\n                  </UIDialogContent>\r\n                </UIDialog>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n\r\n        <div className=\"flex justify-end gap-2\">\r\n          <Button variant=\"outline\" onClick={() => onOpenChange(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={handleSubmit} disabled={loading}>\r\n            {loading ? 'Updating...' : 'Update Order'}\r\n          </Button>\r\n        </div>\r\n      </DialogContent>\r\n\r\n      {/* Create Shipment Dialog */}\r\n      <UIDialog open={showCreateShipment} onOpenChange={setShowCreateShipment}>\r\n        <UIDialogContent>\r\n          <UIDialogHeader>\r\n            <UIDialogTitle>Create Shipment</UIDialogTitle>\r\n          </UIDialogHeader>\r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <Label>Carrier *</Label>\r\n              <Input\r\n                value={newShipment.carrier}\r\n                onChange={(e) => setNewShipment({ ...newShipment, carrier: e.target.value })}\r\n                placeholder=\"e.g., FedEx, UPS, DHL\"\r\n                required\r\n              />\r\n            </div>\r\n            <div>\r\n              <Label>Tracking Number</Label>\r\n              <Input\r\n                value={newShipment.trackingNumber}\r\n                onChange={(e) => setNewShipment({ ...newShipment, trackingNumber: e.target.value })}\r\n                placeholder=\"Enter tracking number\"\r\n              />\r\n            </div>\r\n            <div>\r\n              <Label>Tracking URL</Label>\r\n              <Input\r\n                value={newShipment.trackingUrl}\r\n                onChange={(e) => setNewShipment({ ...newShipment, trackingUrl: e.target.value })}\r\n                placeholder=\"Enter tracking URL\"\r\n              />\r\n            </div>\r\n            <div>\r\n              <Label>Status</Label>\r\n              <Select\r\n                value={newShipment.status}\r\n                onValueChange={(value) => setNewShipment({ ...newShipment, status: value })}\r\n              >\r\n                <SelectTrigger>\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"PENDING\">Pending</SelectItem>\r\n                  <SelectItem value=\"SHIPPED\">Shipped</SelectItem>\r\n                  <SelectItem value=\"IN_TRANSIT\">In Transit</SelectItem>\r\n                  <SelectItem value=\"DELIVERED\">Delivered</SelectItem>\r\n                  <SelectItem value=\"RETURNED\">Returned</SelectItem>\r\n                  <SelectItem value=\"CANCELLED\">Cancelled</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n            <div className=\"flex justify-end gap-2\">\r\n              <Button variant=\"outline\" onClick={() => setShowCreateShipment(false)}>\r\n                Cancel\r\n              </Button>\r\n              <Button onClick={handleCreateShipment} disabled={!newShipment.carrier}>\r\n                Create Shipment\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </UIDialogContent>\r\n      </UIDialog>\r\n    </Dialog>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AApBA;;;;;;;;;;;;;;;;AA8BO,SAAS,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAwB;IAC5F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9E,iBAAiB;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,SAAS;QACT,gBAAgB;QAChB,aAAa;QACb,QAAQ;IACV;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,UAAU,MAAM,MAAM;YACtB,kBAAkB,MAAM,cAAc;YACtC,kBAAkB,MAAM,cAAc;YACtC,aAAa,MAAM,SAAS;YAC5B,SAAS;YACT;QACF;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO;QAEZ,oBAAoB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,iBAAiB,CAAC,MAAM,EAAE;YACrD,IAAI,SAAS,OAAO,EAAE;gBACpB,aAAa,SAAS,IAAI,IAAI,EAAE;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,cAAc,CAAC;gBACxC,SAAS,MAAM,EAAE;gBACjB,GAAG,WAAW;YAChB;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,sBAAsB;gBACtB,eAAe;oBACb,SAAS;oBACT,gBAAgB;oBAChB,aAAa;oBACb,QAAQ;gBACV;gBACA;gBACA;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB,OAAO,YAAoB;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,sBAAsB,CAAC,YAAY;YAE9D,IAAI,SAAS,OAAO,EAAE;gBACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;gBACA;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,cAAc,CAAC;YAE1C,IAAI,SAAS,OAAO,EAAE;gBACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;gBACA;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,WAAW;YAEX,MAAM,aAAa;gBACjB;gBACA,gBAAgB,eAAe,QAAQ;gBACvC,gBAAgB,eAAe,QAAQ;gBACvC,WAAW,UAAU,QAAQ;YAC/B;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;YAEjD,IAAI,SAAS,OAAO,EAAE;gBACpB,uBAAuB;gBACvB,IAAI,MAAM,IAAI,IAAI;oBAChB,MAAM,iHAAA,CAAA,MAAG,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,MAAM,IAAI;gBAC7C;gBAEA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,GAAG;QAC9D,iBAAiB;QACjB,eAAe;QACf,IAAI;YACF,MAAM,UAAU,MAAM,QAAQ,CAAC,EAAE;YACjC,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,cAAc;YACvE,IAAI,SAAS,OAAO,EAAE;gBACpB,oBAAoB;gBACpB,gBAAgB;gBAChB,gBAAgB;gBAChB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,eAAe,SAAS,KAAK,IAAI;YACnC;QACF,EAAE,OAAO,GAAG;YACV,eAAe;QACjB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,2BAA2B,OAAO,UAAkB;QACxD,uBAAuB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,kBAAkB,CAAC,UAAU;YACxD,IAAI,SAAS,OAAO,EAAE;gBACpB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,GAAG;YACV,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,WAAW,MAAM,QAAQ,IAAI;IACnC,MAAM,cAAc,OAAO,YAAY,KAAK,OAAO,kBAAkB,KAAK,OAAO,kBAAkB,KAAK,OAAO,aAAa;IAE5H,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;;oCAAC;oCAAa,MAAM,WAAW;;;;;;;0CAC3C,8OAAC,kIAAA,CAAA,oBAAiB;0CAAC;;;;;;;;;;;;kCAKrB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAU,WAAU;;0CACrC,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAU;;;;;;kDAC7B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAQ;;;;;;kDAC3B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAQ;;;;;;kDAC3B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAU;;;;;;;;;;;;0CAG/B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAS;;;;;;8EACxB,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAQ,eAAe;;sFACpC,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;sFAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8FACZ,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAU;;;;;;8FAC5B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAa;;;;;;8FAC/B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAU;;;;;;8FAC5B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAY;;;;;;8FAC9B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAY;;;;;;8FAC9B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAW;;;;;;8FAC7B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAU;;;;;;;;;;;;;;;;;;;;;;;;sEAKlC,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;8EAAC;;;;;;8EACP,8OAAC;oEAAI,WAAU;8EACZ,MAAM,QAAQ,GACb,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,QAAQ,EAAE,GACxD;;;;;;;;;;;;;;;;;;8DAMR,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;;gFAAK;gFAAE,OAAO,YAAY,GAAG,OAAO,CAAC;;;;;;;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC,iIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EACjE,WAAU;4EACV,KAAI;4EACJ,MAAK;;;;;;;;;;;;8EAGT,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC,iIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EACjE,WAAU;4EACV,KAAI;4EACJ,MAAK;;;;;;;;;;;;8EAGT,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC,iIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EAC5D,WAAU;4EACV,KAAI;4EACJ,MAAK;;;;;;;;;;;;8EAGT,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;;gFAAK;gFAAE,OAAO,eAAe,GAAG,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8DAK/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAQ;;;;;;sEACvB,8OAAC,oIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOlD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;0CACnC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAInC,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,IACnC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBACf,8OAAC;wDAAkB,WAAU;;0EAC3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EACZ,KAAK,OAAO,EAAE,SAAS,QAAQ;4EAAkB;4EAAI,KAAK,OAAO,EAAE,QAAQ;;;;;;;kFAE9E,8OAAC;wEAAI,WAAU;;4EAAgC;4EACvC,KAAK,OAAO,EAAE,OAAO;4EAC1B,MAAM,QAAQ,EAAE,gBAAgB,KAAK,OAAO,EAAE,iBAAiB,KAAK,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,mBAClG,8OAAC;gFAAK,WAAU;;oFAAO;oFACD,MAAM,QAAQ,CAAC,YAAY;oFAAC;;;;;;;;;;;;;oEAIrD,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,aAAa,IAAI,KAAK,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,mBACjF,8OAAC;wEAAI,WAAU;;4EAAqC;4EAEjD,KAAK,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA,mBAC9B,8OAAC;oFAAiB,WAAU;;wFACzB,GAAG,YAAY;wFAAC;wFAAI,GAAG,SAAS,IAAI,GAAG,YAAY;;mFAD3C,GAAG,EAAE;;;;;;;;;;;;;;;;;0EAOxB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;4EAAU;4EAAM,KAAK,QAAQ;;;;;;;kFAC7C,8OAAC;wEAAK,WAAU;;4EAAU;4EAAQ,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC;;;;;;;kFACjE,8OAAC;wEAAK,WAAU;;4EAAsB;4EAAE,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC;;;;;;;;;;;;;;uDA3BlE,KAAK,EAAE;;;;8EAgCnB,8OAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhE,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAItC,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,IACzC,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,8OAAC;wDAAqB,WAAU;;0EAC9B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,QAAQ,aAAa;;;;;;kFAExB,8OAAC;wEAAI,WAAU;kFACZ,QAAQ,MAAM,GAAG,IAAI,KAAK,QAAQ,MAAM,EAAE,kBAAkB,KAAK;;;;;;;;;;;;0EAGtE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAS,QAAQ,MAAM,KAAK,SAAS,YAAY;kFACrD,QAAQ,MAAM;;;;;;kFAEjB,8OAAC;wEAAK,WAAU;;4EAAsB;4EAAE,OAAO,QAAQ,MAAM,IAAI,GAAG,OAAO,CAAC;;;;;;;;;;;;;;uDAbtE,QAAQ,EAAE;;;;8EAkBtB,8OAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhE,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIjC,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAI,WAAU;kFACZ,MAAM,cAAc,iBACnB,8OAAC;;8FACC,8OAAC;;wFAAK,MAAM,cAAc,CAAC,SAAS;wFAAC;wFAAE,MAAM,cAAc,CAAC,QAAQ;;;;;;;8FACpE,8OAAC;8FAAK,MAAM,cAAc,CAAC,QAAQ;;;;;;gFAClC,MAAM,cAAc,CAAC,QAAQ,kBAAI,8OAAC;8FAAK,MAAM,cAAc,CAAC,QAAQ;;;;;;8FACrE,8OAAC;;wFAAK,MAAM,cAAc,CAAC,IAAI;wFAAC;wFAAG,MAAM,cAAc,CAAC,KAAK;wFAAC;wFAAE,MAAM,cAAc,CAAC,UAAU;;;;;;;8FAC/F,8OAAC;8FAAK,MAAM,cAAc,CAAC,OAAO;;;;;;;;;;;mFAGpC;;;;;;;;;;;;0EAKN,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAI,WAAU;kFACZ,MAAM,eAAe,iBACpB,8OAAC;;8FACC,8OAAC;;wFAAK,MAAM,eAAe,CAAC,SAAS;wFAAC;wFAAE,MAAM,eAAe,CAAC,QAAQ;;;;;;;8FACtE,8OAAC;8FAAK,MAAM,eAAe,CAAC,QAAQ;;;;;;gFACnC,MAAM,eAAe,CAAC,QAAQ,kBAAI,8OAAC;8FAAK,MAAM,eAAe,CAAC,QAAQ;;;;;;8FACvE,8OAAC;;wFAAK,MAAM,eAAe,CAAC,IAAI;wFAAC;wFAAG,MAAM,eAAe,CAAC,KAAK;wFAAC;wFAAE,MAAM,eAAe,CAAC,UAAU;;;;;;;8FAClG,8OAAC;8FAAK,MAAM,eAAe,CAAC,OAAO;;;;;;;;;;;mFAGrC;;;;;;;;;;;;;;;;;;kEAMR,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,sBAAsB;kFACtC;;;;;;;;;;;;4DAKF,iCACC,8OAAC;gEAAI,WAAU;0EAAyC;;;;;uEAGtD,UAAU,MAAM,GAAG,IACrB,UAAU,GAAG,CAAC,CAAC,yBACb,8OAAC;oEAAsB,WAAU;;sFAC/B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGAAe,SAAS,OAAO;;;;;;sGAC9C,8OAAC;4FAAI,WAAU;;gGAAgC;gGAClC,SAAS,cAAc,IAAI;;;;;;;wFAEvC,SAAS,WAAW,kBACnB,8OAAC;4FACC,MAAM,SAAS,WAAW;4FAC1B,QAAO;4FACP,KAAI;4FACJ,WAAU;sGACX;;;;;;;;;;;;8FAKL,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAS,SAAS,MAAM,KAAK,cAAc,YAAY;sGAC3D,SAAS,MAAM;;;;;;sGAElB,8OAAC,kIAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,SAAS,IAAM,qBAAqB,SAAS,EAAE;sGAChD;;;;;;;;;;;;;;;;;;sFAML,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;;sGACC,8OAAC,iIAAA,CAAA,QAAK;4FAAC,WAAU;sGAAU;;;;;;sGAC3B,8OAAC,iIAAA,CAAA,QAAK;4FACJ,OAAO,SAAS,cAAc,IAAI;4FAClC,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,EAAE;oGAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gGAAC;4FACpF,aAAY;4FACZ,WAAU;;;;;;;;;;;;8FAGd,8OAAC;;sGACC,8OAAC,iIAAA,CAAA,QAAK;4FAAC,WAAU;sGAAU;;;;;;sGAC3B,8OAAC,iIAAA,CAAA,QAAK;4FACJ,OAAO,SAAS,WAAW,IAAI;4FAC/B,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,EAAE;oGAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gGAAC;4FACjF,aAAY;4FACZ,WAAU;;;;;;;;;;;;8FAGd,8OAAC;;sGACC,8OAAC,iIAAA,CAAA,QAAK;4FAAC,WAAU;sGAAU;;;;;;sGAC3B,8OAAC,kIAAA,CAAA,SAAM;4FACL,OAAO,SAAS,MAAM;4FACtB,eAAe,CAAC,QAAU,qBAAqB,SAAS,EAAE,EAAE;oGAAE,QAAQ;gGAAM;;8GAE5E,8OAAC,kIAAA,CAAA,gBAAa;oGAAC,WAAU;8GACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8GAEd,8OAAC,kIAAA,CAAA,gBAAa;;sHACZ,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAU;;;;;;sHAC5B,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAU;;;;;;sHAC5B,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAa;;;;;;sHAC/B,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAY;;;;;;sHAC9B,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAW;;;;;;sHAC7B,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAY;;;;;;;;;;;;;;;;;;;;;;;;8FAIpC,8OAAC;;sGACC,8OAAC,iIAAA,CAAA,QAAK;4FAAC,WAAU;sGAAU;;;;;;sGAC3B,8OAAC,iIAAA,CAAA,QAAK;4FACJ,OAAO,SAAS,OAAO;4FACvB,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,EAAE;oGAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gGAAC;4FAC7E,aAAY;4FACZ,WAAU;;;;;;;;;;;;;;;;;;sFAKhB,8OAAC;4EAAI,WAAU;;gFAAgC;gFACnC,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;gFACpD,SAAS,SAAS,kBACjB;;wFAAE;wFAAa,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;;;gFAE3D,SAAS,WAAW,kBACnB;;wFAAE;wFAAe,IAAI,KAAK,SAAS,WAAW,EAAE,cAAc;;;;;;;;;;mEAvF1D,SAAS,EAAE;;;;0FA6FvB,8OAAC;gEAAE,WAAU;0EAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlE,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;0CACnC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,8OAAC,gIAAA,CAAA,cAAW;sDACT,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,kBAC3C,8OAAC;gDAAI,WAAU;0DACZ,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,oBACpB,8OAAC;wDAAiB,WAAU;kEAC1B,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM;;;;;;8EAE5B,8OAAC;oEAAI,WAAU;;wEACZ,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG;wEAC9E;wEACA,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;8EACZ,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,yBACrC,8OAAC;wEAAI,WAAU;kFAAgE,KAAK,SAAS,CAAC,IAAI,OAAO,EAAE,MAAM;;;;;6FAEjH,8OAAC;kFAAM,IAAI,OAAO;;;;;;;;;;;;;;;;;uDAdhB,IAAI,EAAE;;;;;;;;;qEAsBpB,8OAAC;gDAAE,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;0CAM9D,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAItC,8OAAC,gIAAA,CAAA,cAAW;;gDACT,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG,mBAC1E,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,UAClB,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,KAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAA,uBACnE,8OAAC;gEAAoB,WAAU;;kFAC7B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;oFAAc;oFACjB,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC;;;;;;;0FAE1C,8OAAC;gFAAI,WAAU;;oFAAgC;oFACpC,OAAO,MAAM;oFACrB,OAAO,UAAU,kBAChB;;4FACG;4FAAM;4FAAW,IAAI,KAAK,OAAO,UAAU,EAAE,cAAc;;;;;;;;;0FAIlE,8OAAC;gFAAI,WAAU;0FACZ,OAAO,MAAM;;;;;;;;;;;;kFAIlB,8OAAC;wEAAI,WAAU;kFACZ;4EAAC;4EAAW;yEAAW,CAAC,QAAQ,CAAC,OAAO,MAAM,mBAC7C;;8FACE,8OAAC,kIAAA,CAAA,SAAM;oFAAC,MAAK;oFAAK,SAAQ;oFAAU,UAAU,wBAAwB,OAAO,EAAE;oFAAE,SAAS,IAAM,yBAAyB,OAAO,EAAE,EAAE;8FAAa;;;;;;8FACjJ,8OAAC,kIAAA,CAAA,SAAM;oFAAC,MAAK;oFAAK,SAAQ;oFAAU,UAAU,wBAAwB,OAAO,EAAE;oFAAE,SAAS,IAAM,yBAAyB,OAAO,EAAE,EAAE;8FAAc;;;;;;8FAClJ,8OAAC,kIAAA,CAAA,SAAM;oFAAC,MAAK;oFAAK,SAAQ;oFAAc,UAAU,wBAAwB,OAAO,EAAE;oFAAE,SAAS,IAAM,yBAAyB,OAAO,EAAE,EAAE;8FAAa;;;;;;;;;;;;;;+DAvBnJ,OAAO,EAAE;;;;;;;;;yEAgCzB,8OAAC;oDAAE,WAAU;8DAAyC;;;;;;8DAGxD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,SAAS,IAAM,oBAAoB;kEAAO;;;;;;;;;;;8DAEtE,8OAAC,kIAAA,CAAA,SAAQ;oDAAC,MAAM;oDAAkB,cAAc;8DAC9C,cAAA,8OAAC,kIAAA,CAAA,gBAAe;;0EACd,8OAAC,kIAAA,CAAA,eAAc;0EACb,cAAA,8OAAC,kIAAA,CAAA,cAAa;8EAAC;;;;;;;;;;;0EAEjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC,iIAAA,CAAA,QAAK;0FAAC;;;;;;0FACP,8OAAC,iIAAA,CAAA,QAAK;gFAAC,MAAK;gFAAS,KAAK;gFAAG,KAAK,MAAM,WAAW;gFAAE,OAAO;gFAAc,UAAU,CAAA,IAAK,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kFAEhI,8OAAC;;0FACC,8OAAC,iIAAA,CAAA,QAAK;0FAAC;;;;;;0FACP,8OAAC,oIAAA,CAAA,WAAQ;gFAAC,OAAO;gFAAc,UAAU,CAAA,IAAK,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;oEAE7E,6BAAe,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kIAAA,CAAA,SAAM;gFAAC,SAAQ;gFAAU,SAAS,IAAM,oBAAoB;0FAAQ;;;;;;0FACrE,8OAAC,kIAAA,CAAA,SAAM;gFAAC,SAAS;gFAAsB,UAAU,iBAAiB,gBAAgB;0FAAI,gBAAgB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUxI,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,aAAa;0CAAQ;;;;;;0CAG9D,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;0CACtC,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;0BAMjC,8OAAC,kIAAA,CAAA,SAAQ;gBAAC,MAAM;gBAAoB,cAAc;0BAChD,cAAA,8OAAC,kIAAA,CAAA,gBAAe;;sCACd,8OAAC,kIAAA,CAAA,eAAc;sCACb,cAAA,8OAAC,kIAAA,CAAA,cAAa;0CAAC;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,iIAAA,CAAA,QAAK;4CACJ,OAAO,YAAY,OAAO;4CAC1B,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC1E,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAGZ,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,iIAAA,CAAA,QAAK;4CACJ,OAAO,YAAY,cAAc;4CACjC,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACjF,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,iIAAA,CAAA,QAAK;4CACJ,OAAO,YAAY,WAAW;4CAC9B,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC9E,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,YAAY,MAAM;4CACzB,eAAe,CAAC,QAAU,eAAe;oDAAE,GAAG,WAAW;oDAAE,QAAQ;gDAAM;;8DAEzE,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa;;;;;;sEAC/B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;sEAC9B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAIpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,sBAAsB;sDAAQ;;;;;;sDAGvE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAsB,UAAU,CAAC,YAAY,OAAO;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrF", "debugId": null}}, {"offset": {"line": 8295, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/orders/order-status-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { api, Order } from '@/lib/api';\r\nimport { toast } from 'sonner';\r\n\r\ninterface OrderStatusDialogProps {\r\n  order: Order | null;\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onSuccess: () => void;\r\n}\r\n\r\nconst statusOptions = [\r\n  { value: 'PENDING', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },\r\n  { value: 'PROCESSING', label: 'Processing', color: 'bg-blue-100 text-blue-800' },\r\n  { value: 'SHIPPED', label: 'Shipped', color: 'bg-purple-100 text-purple-800' },\r\n  { value: 'DELIVERED', label: 'Delivered', color: 'bg-green-100 text-green-800' },\r\n  { value: 'CANCELLED', label: 'Cancelled', color: 'bg-red-100 text-red-800' },\r\n  { value: 'REFUNDED', label: 'Refunded', color: 'bg-gray-100 text-gray-800' },\r\n  { value: 'ON_HOLD', label: 'On Hold', color: 'bg-orange-100 text-orange-800' },\r\n];\r\n\r\nexport function OrderStatusDialog({ order, open, onOpenChange, onSuccess }: OrderStatusDialogProps) {\r\n  const [loading, setLoading] = useState(false);\r\n  const [status, setStatus] = useState(order?.status || '');\r\n  const [note, setNote] = useState('');\r\n\r\n  const handleSubmit = async () => {\r\n    if (!order) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      \r\n      const response = await api.updateOrderStatus(order.id, status, note.trim() || undefined);\r\n      \r\n      if (response.success) {\r\n        onSuccess();\r\n        onOpenChange(false);\r\n        setNote('');\r\n        toast.success(`Order status updated to ${status}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update order status:', error);\r\n      toast.error('Failed to update order status');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    if (!loading) {\r\n      setNote('');\r\n      onOpenChange(false);\r\n    }\r\n  };\r\n\r\n  if (!order) return null;\r\n\r\n  const currentStatus = statusOptions.find(s => s.value === order.status);\r\n  const newStatus = statusOptions.find(s => s.value === status);\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={handleClose}>\r\n      <DialogContent className=\"max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle>Update Order Status</DialogTitle>\r\n          <DialogDescription>\r\n            Update the status for order #{order.orderNumber}\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4\">\r\n          <div>\r\n            <Label>Current Status</Label>\r\n            <div className=\"mt-1\">\r\n              <Badge variant=\"outline\" className={currentStatus?.color}>\r\n                {currentStatus?.label}\r\n              </Badge>\r\n            </div>\r\n          </div>\r\n\r\n          <div>\r\n            <Label htmlFor=\"status\">New Status</Label>\r\n            <Select value={status} onValueChange={setStatus}>\r\n              <SelectTrigger>\r\n                <SelectValue placeholder=\"Select new status\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {statusOptions.map((option) => (\r\n                  <SelectItem key={option.value} value={option.value}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Badge variant=\"outline\" className={option.color}>\r\n                        {option.label}\r\n                      </Badge>\r\n                    </div>\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n\r\n          {status && status !== order.status && (\r\n            <div className=\"p-3 bg-blue-50 rounded-lg\">\r\n              <div className=\"text-sm font-medium text-blue-800\">\r\n                Status Change Preview\r\n              </div>\r\n              <div className=\"text-sm text-blue-700 mt-1\">\r\n                {currentStatus?.label} → {newStatus?.label}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <div>\r\n            <Label htmlFor=\"note\">Note (Optional)</Label>\r\n            <Textarea\r\n              id=\"note\"\r\n              placeholder=\"Add a note about this status change...\"\r\n              value={note}\r\n              onChange={(e) => setNote(e.target.value)}\r\n              rows={3}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex justify-end gap-2\">\r\n            <Button variant=\"outline\" onClick={handleClose}>\r\n              Cancel\r\n            </Button>\r\n            <Button \r\n              onClick={handleSubmit} \r\n              disabled={loading || !status || status === order.status}\r\n            >\r\n              {loading ? 'Updating...' : 'Update Status'}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;AAyBA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;IAAgC;IAC7E;QAAE,OAAO;QAAc,OAAO;QAAc,OAAO;IAA4B;IAC/E;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;IAAgC;IAC7E;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAA0B;IAC3E;QAAE,OAAO;QAAY,OAAO;QAAY,OAAO;IAA4B;IAC3E;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;IAAgC;CAC9E;AAEM,SAAS,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAA0B;IAChG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,UAAU;IACtD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,WAAW;YAEX,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,QAAQ,KAAK,IAAI,MAAM;YAE9E,IAAI,SAAS,OAAO,EAAE;gBACpB;gBACA,aAAa;gBACb,QAAQ;gBACR,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,wBAAwB,EAAE,QAAQ;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,QAAQ;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,MAAM,MAAM;IACtE,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAEtD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACa,MAAM,WAAW;;;;;;;;;;;;;8BAInD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAW,eAAe;kDAChD,eAAe;;;;;;;;;;;;;;;;;sCAKtB,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAS;;;;;;8CACxB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAQ,eAAe;;sDACpC,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,kIAAA,CAAA,aAAU;oDAAoB,OAAO,OAAO,KAAK;8DAChD,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAW,OAAO,KAAK;sEAC7C,OAAO,KAAK;;;;;;;;;;;mDAHF,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;wBAYpC,UAAU,WAAW,MAAM,MAAM,kBAChC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAoC;;;;;;8CAGnD,8OAAC;oCAAI,WAAU;;wCACZ,eAAe;wCAAM;wCAAI,WAAW;;;;;;;;;;;;;sCAK3C,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACvC,MAAM;;;;;;;;;;;;sCAIV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAa;;;;;;8CAGhD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,WAAW,CAAC,UAAU,WAAW,MAAM,MAAM;8CAEtD,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 8627, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/components/ui/date-range-picker.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { CalendarDays } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface DatePickerWithRangeProps {\r\n  value: {\r\n    from: Date | undefined;\r\n    to: Date | undefined;\r\n  };\r\n  onChange: (range: { from: Date | undefined; to: Date | undefined }) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n}\r\n\r\nexport function DatePickerWithRange({ \r\n  value, \r\n  onChange, \r\n  placeholder = \"Pick a date range\",\r\n  className \r\n}: DatePickerWithRangeProps) {\r\n  const [open, setOpen] = useState(false);\r\n\r\n  const handleFromDateChange = (date: string) => {\r\n    const newDate = date ? new Date(date) : undefined;\r\n    onChange({ from: newDate, to: value.to });\r\n  };\r\n\r\n  const handleToDateChange = (date: string) => {\r\n    const newDate = date ? new Date(date) : undefined;\r\n    onChange({ from: value.from, to: newDate });\r\n  };\r\n\r\n  const handleClear = () => {\r\n    onChange({ from: undefined, to: undefined });\r\n  };\r\n\r\n  const formatDate = (date: Date | undefined) => {\r\n    if (!date) return '';\r\n    return date.toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const getDisplayText = () => {\r\n    if (value.from && value.to) {\r\n      return `${formatDate(value.from)} - ${formatDate(value.to)}`;\r\n    }\r\n    if (value.from) {\r\n      return `From ${formatDate(value.from)}`;\r\n    }\r\n    if (value.to) {\r\n      return `Until ${formatDate(value.to)}`;\r\n    }\r\n    return placeholder;\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          className={cn(\r\n            \"justify-start text-left font-normal\",\r\n            !value.from && !value.to && \"text-muted-foreground\",\r\n            className\r\n          )}\r\n        >\r\n          <CalendarDays className=\"mr-2 h-4 w-4\" />\r\n          {getDisplayText()}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-auto p-4\" align=\"start\">\r\n        <div className=\"space-y-4\">\r\n          <div className=\"grid gap-2\">\r\n            <label htmlFor=\"from-date\" className=\"text-sm font-medium\">\r\n              From Date\r\n            </label>\r\n            <input\r\n              id=\"from-date\"\r\n              type=\"date\"\r\n              value={value.from ? value.from.toISOString().split('T')[0] : ''}\r\n              onChange={(e) => handleFromDateChange(e.target.value)}\r\n              className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"grid gap-2\">\r\n            <label htmlFor=\"to-date\" className=\"text-sm font-medium\">\r\n              To Date\r\n            </label>\r\n            <input\r\n              id=\"to-date\"\r\n              type=\"date\"\r\n              value={value.to ? value.to.toISOString().split('T')[0] : ''}\r\n              onChange={(e) => handleToDateChange(e.target.value)}\r\n              className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex justify-between\">\r\n            <Button variant=\"outline\" size=\"sm\" onClick={handleClear}>\r\n              Clear\r\n            </Button>\r\n            <Button size=\"sm\" onClick={() => setOpen(false)}>\r\n              Apply\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAkBO,SAAS,oBAAoB,EAClC,KAAK,EACL,QAAQ,EACR,cAAc,mBAAmB,EACjC,SAAS,EACgB;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,uBAAuB,CAAC;QAC5B,MAAM,UAAU,OAAO,IAAI,KAAK,QAAQ;QACxC,SAAS;YAAE,MAAM;YAAS,IAAI,MAAM,EAAE;QAAC;IACzC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,UAAU,OAAO,IAAI,KAAK,QAAQ;QACxC,SAAS;YAAE,MAAM,MAAM,IAAI;YAAE,IAAI;QAAQ;IAC3C;IAEA,MAAM,cAAc;QAClB,SAAS;YAAE,MAAM;YAAW,IAAI;QAAU;IAC5C;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,MAAM,IAAI,IAAI,MAAM,EAAE,EAAE;YAC1B,OAAO,GAAG,WAAW,MAAM,IAAI,EAAE,GAAG,EAAE,WAAW,MAAM,EAAE,GAAG;QAC9D;QACA,IAAI,MAAM,IAAI,EAAE;YACd,OAAO,CAAC,KAAK,EAAE,WAAW,MAAM,IAAI,GAAG;QACzC;QACA,IAAI,MAAM,EAAE,EAAE;YACZ,OAAO,CAAC,MAAM,EAAE,WAAW,MAAM,EAAE,GAAG;QACxC;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uCACA,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,yBAC5B;;sCAGF,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBACvB;;;;;;;;;;;;0BAGL,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAa,OAAM;0BAC3C,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAY,WAAU;8CAAsB;;;;;;8CAG3D,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;oCAC7D,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oCACpD,WAAU;;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAU,WAAU;8CAAsB;;;;;;8CAGzD,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;oCACzD,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS;8CAAa;;;;;;8CAG1D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS,IAAM,QAAQ;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D", "debugId": null}}, {"offset": {"line": 8830, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nik/centre_research_peptides_ecommerce_monorepo/nextjs-frontend/src/app/orders/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { DashboardLayout } from '@/components/dashboard/dashboard-layout';\r\nimport { ProtectedRoute } from '@/contexts/auth-context';\r\nimport { OrdersTable } from '@/components/orders/orders-table';\r\nimport { CreateOrderDialog } from '@/components/orders/create-order-dialog';\r\nimport { EditOrderDialog } from '@/components/orders/edit-order-dialog';\r\nimport { OrderStatusDialog } from '@/components/orders/order-status-dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { DatePickerWithRange } from '@/components/ui/date-range-picker';\r\nimport { \r\n  Plus, \r\n  Search, \r\n  ShoppingCart, \r\n  Clock, \r\n  Truck, \r\n  CheckCircle, \r\n  XCircle,\r\n  DollarSign,\r\n  Package\r\n} from 'lucide-react';\r\nimport { api, Order } from '@/lib/api';\r\nimport { toast } from 'sonner';\r\n\r\nexport default function OrdersPage() {\r\n  const [orders, setOrders] = useState<Order[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [statusFilter, setStatusFilter] = useState<string>('all');\r\n  const [customerFilter, setCustomerFilter] = useState<string>('all');\r\n  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({\r\n    from: undefined,\r\n    to: undefined,\r\n  });\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalOrders, setTotalOrders] = useState(0);\r\n  const [totalPages, setTotalPages] = useState(0);\r\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\r\n  const [editingOrder, setEditingOrder] = useState<Order | null>(null);\r\n  const [statusOrder, setStatusOrder] = useState<Order | null>(null);\r\n  \r\n  const ITEMS_PER_PAGE = 10;\r\n\r\n  const fetchOrders = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const params = {\r\n        page: currentPage,\r\n        limit: ITEMS_PER_PAGE,\r\n        search: searchTerm || undefined,\r\n        status: statusFilter !== 'all' ? statusFilter : undefined,\r\n        customerId: customerFilter !== 'all' ? customerFilter : undefined,\r\n        dateFrom: dateRange.from?.toISOString(),\r\n        dateTo: dateRange.to?.toISOString(),\r\n      };\r\n\r\n      const response = await api.getOrders(params);\r\n      \r\n      if (response.success && response.data) {\r\n        setOrders(response.data.orders || []);\r\n        setTotalOrders(response.data.pagination.total);\r\n        setTotalPages(response.data.pagination.pages);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to fetch orders:', error);\r\n      toast.error('Failed to load orders');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchOrders();\r\n  }, [currentPage, searchTerm, statusFilter, customerFilter, dateRange]);\r\n\r\n  const handleSearch = (value: string) => {\r\n    setSearchTerm(value);\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  const handleStatusFilter = (value: string) => {\r\n    setStatusFilter(value);\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  const handleCustomerFilter = (value: string) => {\r\n    setCustomerFilter(value);\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  const handleDateRangeChange = (range: { from: Date | undefined; to: Date | undefined }) => {\r\n    setDateRange(range);\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  const handleOrderCreated = () => {\r\n    setShowCreateDialog(false);\r\n    fetchOrders();\r\n    toast.success('Order created successfully');\r\n  };\r\n\r\n  const handleOrderUpdated = () => {\r\n    setEditingOrder(null);\r\n    fetchOrders();\r\n    toast.success('Order updated successfully');\r\n  };\r\n\r\n  const handleStatusUpdated = () => {\r\n    setStatusOrder(null);\r\n    fetchOrders();\r\n    toast.success('Order status updated successfully');\r\n  };\r\n\r\n  const handleDeleteOrder = async (orderId: string) => {\r\n    try {\r\n      const response = await api.deleteOrder(orderId);\r\n      if (response.success) {\r\n        fetchOrders();\r\n        toast.success('Order deleted successfully');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete order:', error);\r\n      toast.error('Failed to delete order');\r\n    }\r\n  };\r\n\r\n  const handleUpdateStatus = (order: Order) => {\r\n    setStatusOrder(order);\r\n  };\r\n\r\n  // Calculate stats\r\n  const stats = {\r\n    total: totalOrders,\r\n    pending: orders.filter(o => o.status === 'PENDING').length,\r\n    processing: orders.filter(o => o.status === 'PROCESSING').length,\r\n    shipped: orders.filter(o => o.status === 'SHIPPED').length,\r\n    delivered: orders.filter(o => o.status === 'DELIVERED').length,\r\n    cancelled: orders.filter(o => o.status === 'CANCELLED').length,\r\n    totalRevenue: orders.reduce((sum, o) => sum + (o.totalAmount || 0), 0),\r\n  };\r\n\r\n  return (\r\n    <ProtectedRoute requiredRoles={['ADMIN', 'MANAGER', 'STAFF']}>\r\n      <DashboardLayout>\r\n        <div className=\"space-y-6\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold tracking-tight\">Orders</h1>\r\n              <p className=\"text-muted-foreground\">\r\n                Manage orders and fulfillment process\r\n              </p>\r\n            </div>\r\n            <Button onClick={() => setShowCreateDialog(true)}>\r\n              <Plus className=\"mr-2 h-4 w-4\" />\r\n              Create Order\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Stats Cards */}\r\n          <div className=\"grid gap-4 md:grid-cols-7\">\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">Total Orders</CardTitle>\r\n                <ShoppingCart className=\"h-4 w-4 text-muted-foreground\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold\">{stats.total}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">Pending</CardTitle>\r\n                <Clock className=\"h-4 w-4 text-yellow-600\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold text-yellow-600\">{stats.pending}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">Processing</CardTitle>\r\n                <Package className=\"h-4 w-4 text-blue-600\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold text-blue-600\">{stats.processing}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">Shipped</CardTitle>\r\n                <Truck className=\"h-4 w-4 text-purple-600\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold text-purple-600\">{stats.shipped}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">Delivered</CardTitle>\r\n                <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold text-green-600\">{stats.delivered}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">Cancelled</CardTitle>\r\n                <XCircle className=\"h-4 w-4 text-red-600\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold text-red-600\">{stats.cancelled}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">Revenue</CardTitle>\r\n                <DollarSign className=\"h-4 w-4 text-green-600\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold text-green-600\">\r\n                  ${Number((stats.totalRevenue ?? 0)).toFixed(2)}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n\r\n          {/* Filters */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Filters</CardTitle>\r\n              <CardDescription>Search and filter orders</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"flex flex-col space-y-4\">\r\n                <div className=\"flex flex-col sm:flex-row gap-4\">\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"relative\">\r\n                      <Search className=\"absolute left-3 top-3 h-4 w-4 text-muted-foreground\" />\r\n                      <Input\r\n                        placeholder=\"Search orders by number, customer, or email...\"\r\n                        value={searchTerm}\r\n                        onChange={(e) => handleSearch(e.target.value)}\r\n                        className=\"pl-10\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <Select value={statusFilter} onValueChange={handleStatusFilter}>\r\n                    <SelectTrigger className=\"w-full sm:w-[180px]\">\r\n                      <SelectValue placeholder=\"Filter by status\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem value=\"all\">All Status</SelectItem>\r\n                      <SelectItem value=\"PENDING\">Pending</SelectItem>\r\n                      <SelectItem value=\"PROCESSING\">Processing</SelectItem>\r\n                      <SelectItem value=\"SHIPPED\">Shipped</SelectItem>\r\n                      <SelectItem value=\"DELIVERED\">Delivered</SelectItem>\r\n                      <SelectItem value=\"CANCELLED\">Cancelled</SelectItem>\r\n                      <SelectItem value=\"REFUNDED\">Refunded</SelectItem>\r\n                      <SelectItem value=\"ON_HOLD\">On Hold</SelectItem>\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n                <div className=\"flex flex-col sm:flex-row gap-4\">\r\n                  <DatePickerWithRange\r\n                    value={dateRange}\r\n                    onChange={handleDateRangeChange}\r\n                    placeholder=\"Select date range\"\r\n                    className=\"w-full sm:w-[300px]\"\r\n                  />\r\n                  <Select value={customerFilter} onValueChange={handleCustomerFilter}>\r\n                    <SelectTrigger className=\"w-full sm:w-[200px]\">\r\n                      <SelectValue placeholder=\"Filter by customer\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem value=\"all\">All Customers</SelectItem>\r\n                      {/* Customer options would be populated from API */}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Orders Table */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Orders List</CardTitle>\r\n              <CardDescription>\r\n                {loading ? 'Loading...' : `Showing ${orders.length} of ${totalOrders} orders`}\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <OrdersTable\r\n                orders={orders}\r\n                loading={loading}\r\n                onEdit={setEditingOrder}\r\n                onDelete={handleDeleteOrder}\r\n                onUpdateStatus={handleUpdateStatus}\r\n                currentPage={currentPage}\r\n                totalPages={totalPages}\r\n                onPageChange={setCurrentPage}\r\n              />\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Dialogs */}\r\n          <CreateOrderDialog\r\n            open={showCreateDialog}\r\n            onOpenChange={setShowCreateDialog}\r\n            onSuccess={handleOrderCreated}\r\n          />\r\n          \r\n          <EditOrderDialog\r\n            order={editingOrder}\r\n            open={!!editingOrder}\r\n            onOpenChange={(open) => !open && setEditingOrder(null)}\r\n            onSuccess={handleOrderUpdated}\r\n          />\r\n\r\n          <OrderStatusDialog\r\n            order={statusOrder}\r\n            open={!!statusOrder}\r\n            onOpenChange={(open) => !open && setStatusOrder(null)}\r\n            onSuccess={handleStatusUpdated}\r\n          />\r\n        </div>\r\n      </DashboardLayout>\r\n    </ProtectedRoute>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AA3BA;;;;;;;;;;;;;;;;;AA6Be,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoD;QAC3F,MAAM;QACN,IAAI;IACN;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAE7D,MAAM,iBAAiB;IAEvB,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,SAAS;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,cAAc;gBACtB,QAAQ,iBAAiB,QAAQ,eAAe;gBAChD,YAAY,mBAAmB,QAAQ,iBAAiB;gBACxD,UAAU,UAAU,IAAI,EAAE;gBAC1B,QAAQ,UAAU,EAAE,EAAE;YACxB;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,SAAS,CAAC;YAErC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;gBACpC,eAAe,SAAS,IAAI,CAAC,UAAU,CAAC,KAAK;gBAC7C,cAAc,SAAS,IAAI,CAAC,UAAU,CAAC,KAAK;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;QAAY;QAAc;QAAgB;KAAU;IAErE,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,qBAAqB;QACzB,oBAAoB;QACpB;QACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,qBAAqB;QACzB,gBAAgB;QAChB;QACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,sBAAsB;QAC1B,eAAe;QACf;QACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,WAAW,CAAC;YACvC,IAAI,SAAS,OAAO,EAAE;gBACpB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe;IACjB;IAEA,kBAAkB;IAClB,MAAM,QAAQ;QACZ,OAAO;QACP,SAAS,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC1D,YAAY,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,cAAc,MAAM;QAChE,SAAS,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC1D,WAAW,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QAC9D,WAAW,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QAC9D,cAAc,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,WAAW,IAAI,CAAC,GAAG;IACtE;IAEA,qBACE,8OAAC,mIAAA,CAAA,iBAAc;QAAC,eAAe;YAAC;YAAS;YAAW;SAAQ;kBAC1D,cAAA,8OAAC,sJAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,oBAAoB;;kDACzC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;kDAE1B,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,MAAM,KAAK;;;;;;;;;;;;;;;;;0CAGpD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsC,MAAM,OAAO;;;;;;;;;;;;;;;;;0CAGtE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;kDAErB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAoC,MAAM,UAAU;;;;;;;;;;;;;;;;;0CAGvE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsC,MAAM,OAAO;;;;;;;;;;;;;;;;;0CAGtE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAqC,MAAM,SAAS;;;;;;;;;;;;;;;;;0CAGvE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;kDAErB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAmC,MAAM,SAAS;;;;;;;;;;;;;;;;;0CAGrE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;gDAAoC;gDAC/C,OAAQ,MAAM,YAAY,IAAI,GAAI,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAOpD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC5C,WAAU;;;;;;;;;;;;;;;;;8DAIhB,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAc,eAAe;;sEAC1C,8OAAC,kIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;8EACxB,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAa;;;;;;8EAC/B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;8EAC7B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,mJAAA,CAAA,sBAAmB;oDAClB,OAAO;oDACP,UAAU;oDACV,aAAY;oDACZ,WAAU;;;;;;8DAEZ,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAgB,eAAe;;sEAC5C,8OAAC,kIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUpC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDACb,UAAU,eAAe,CAAC,QAAQ,EAAE,OAAO,MAAM,CAAC,IAAI,EAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;0CAGjF,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,+IAAA,CAAA,cAAW;oCACV,QAAQ;oCACR,SAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,gBAAgB;oCAChB,aAAa;oCACb,YAAY;oCACZ,cAAc;;;;;;;;;;;;;;;;;kCAMpB,8OAAC,yJAAA,CAAA,oBAAiB;wBAChB,MAAM;wBACN,cAAc;wBACd,WAAW;;;;;;kCAGb,8OAAC,uJAAA,CAAA,kBAAe;wBACd,OAAO;wBACP,MAAM,CAAC,CAAC;wBACR,cAAc,CAAC,OAAS,CAAC,QAAQ,gBAAgB;wBACjD,WAAW;;;;;;kCAGb,8OAAC,yJAAA,CAAA,oBAAiB;wBAChB,OAAO;wBACP,MAAM,CAAC,CAAC;wBACR,cAAc,CAAC,OAAS,CAAC,QAAQ,eAAe;wBAChD,WAAW;;;;;;;;;;;;;;;;;;;;;;AAMvB", "debugId": null}}]}